"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const auth_service_1 = require("./auth.service");
const jwt_auth_guard_1 = require("./jwt-auth.guard");
const register_dto_1 = require("../../dto/auth/register.dto");
const login_dto_1 = require("../../dto/auth/login.dto");
const throttler_1 = require("@nestjs/throttler");
const jwt_1 = require("@nestjs/jwt");
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    async login(loginDto) {
        const identifier = loginDto.email || loginDto.username || 'unknown';
        console.log(`[AUTH] Login attempt for user: ${identifier}`);
        try {
            // Convert LoginDto to LoginRequest with device information
            // Use email if provided, otherwise use username as email for lookup
            const loginRequest = {
                email: loginDto.email || loginDto.username || '',
                password: loginDto.password,
                deviceId: loginDto.deviceId,
                twoFactorToken: loginDto.twoFactorToken,
                deviceInfo: {
                    name: loginDto.deviceName,
                    type: loginDto.deviceType,
                    os: loginDto.deviceOs,
                    browser: loginDto.deviceBrowser,
                    browserVersion: loginDto.deviceBrowserVersion,
                    token: loginDto.deviceToken,
                    userAgent: loginDto.userAgent,
                    ipAddress: loginDto.ipAddress
                },
                rememberMe: loginDto.rememberMe
            };
            const result = await this.authService.login(loginRequest);
            console.log(`[AUTH] Login successful for user: ${identifier}`);
            return result;
        }
        catch (error) {
            console.error(`[AUTH] Login failed for user: ${identifier}`, error.message);
            throw error;
        }
    }
    async register(userDto) {
        console.log(`[AUTH] Registration attempt for user: ${userDto.email}, name: ${userDto.name}, role: ${userDto.role}, username: ${userDto.username}`);
        try {
            // Convert RegisterDto to RegisterRequest
            const registerRequest = {
                email: userDto.email,
                password: userDto.password,
                firstName: userDto.name.split(' ')[0],
                lastName: userDto.name.split(' ').slice(1).join(' ') || undefined,
                username: userDto.username,
                role: userDto.role
            };
            const result = await this.authService.register(registerRequest);
            console.log(`[AUTH] Registration successful for user: ${userDto.email}`);
            return result;
        }
        catch (error) {
            console.error(`[AUTH] Registration failed for user: ${userDto.email}`, error.message);
            // Check if the error is an UnauthorizedException with a suggested username
            if (error instanceof common_1.UnauthorizedException) {
                const errorResponse = error.getResponse();
                // If the error response is an object with a suggestedUsername property
                if (typeof errorResponse === 'object' && errorResponse && 'suggestedUsername' in errorResponse) {
                    // Pass through the error with the suggested username
                    throw error;
                }
                // Handle other UnauthorizedException cases
                if (typeof errorResponse === 'string') {
                    if (errorResponse.includes('email already exists')) {
                        throw new common_1.UnauthorizedException(`Registration failed: Email ${userDto.email} is already in use`);
                    }
                    else if (errorResponse.includes('username already exists')) {
                        throw new common_1.UnauthorizedException(`Registration failed: Username ${userDto.username} is already in use`);
                    }
                }
            }
            // Handle database constraint errors
            if (error.message && error.message.includes('duplicate key value')) {
                if (error.message.includes('email') || error.message.includes('users_email_key')) {
                    throw new common_1.UnauthorizedException(`Registration failed: Email ${userDto.email} is already in use`);
                }
                else if (error.message.includes('username') || error.message.includes('UQ_c0d176bcc1665dc7cb60482c817')) {
                    throw new common_1.UnauthorizedException({
                        message: `Registration failed: Username ${userDto.username} is already in use`,
                        suggestedUsername: `${userDto.username}${Math.floor(Math.random() * 900) + 100}`
                    });
                }
                else {
                    throw new common_1.UnauthorizedException('Registration failed: A user with these details already exists');
                }
            }
            throw error;
        }
    }
    protectedRoute() {
        return { message: 'This is a protected route' };
    }
    async getSession(req) {
        // Check if there's an authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return { user: null };
        }
        try {
            // Extract the token
            const token = authHeader.split(' ')[1];
            // Validate the token and get user info
            const user = await this.authService.validateToken(token);
            return { user };
        }
        catch (error) {
            return { user: null };
        }
    }
    async getProfile(req) {
        console.log('[AUTH-CONTROLLER] Profile request received, user:', req.user);
        try {
            return req.user;
        }
        catch (error) {
            console.error('[AUTH-CONTROLLER] Error in getProfile:', error.message);
            throw error;
        }
    }
    async testAuth() {
        return { message: 'This endpoint does not require authentication' };
    }
    async testToken(authHeader) {
        console.log('[AUTH-CONTROLLER] Test token endpoint accessed, authHeader:', authHeader);
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return { message: 'No token provided' };
        }
        const token = authHeader.substring(7);
        try {
            // Manually verify the token
            const jwtService = new jwt_1.JwtService({
                secret: 'medical-app-secure-jwt-secret-key-2025-updated-with-stronger-key-for-security'
            });
            const decoded = jwtService.verify(token);
            console.log('[AUTH-CONTROLLER] Decoded token:', decoded);
            return {
                message: 'Token is valid',
                decoded
            };
        }
        catch (error) {
            console.error('[AUTH-CONTROLLER] Error verifying token:', error.message);
            return {
                message: 'Invalid token',
                error: error.message
            };
        }
    }
    async testProtected(req) {
        console.log('[AUTH-CONTROLLER] Test protected endpoint accessed, user:', req.user);
        return { message: 'This endpoint is protected and requires authentication', user: req.user };
    }
    async createTestUsers() {
        console.log('[AUTH-CONTROLLER] Creating test users');
        try {
            // Create test user directly using auth service
            const testUser = await this.authService.register({
                email: '<EMAIL>',
                username: 'test',
                password: 'test',
                firstName: 'Test',
                lastName: 'User',
                role: 'student'
            });
            // Create admin user directly using auth service
            const adminUser = await this.authService.register({
                email: '<EMAIL>',
                username: 'admin',
                password: 'admin',
                firstName: 'Admin',
                lastName: 'User',
                role: 'admin'
            });
            return {
                message: 'Test users created successfully',
                testUser: testUser?.user,
                adminUser: adminUser?.user
            };
        }
        catch (error) {
            console.error('[AUTH-CONTROLLER] Error creating test users:', error.message);
            return {
                message: 'Error creating test users',
                error: error.message
            };
        }
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('login'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [login_dto_1.LoginDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.Post)('register'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [register_dto_1.RegisterDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('protected'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AuthController.prototype, "protectedRoute", null);
__decorate([
    (0, common_1.Get)('session'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getSession", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('profile'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Get)('test-auth'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "testAuth", null);
__decorate([
    (0, common_1.Get)('test-token'),
    __param(0, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "testToken", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('test-protected'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "testProtected", null);
__decorate([
    (0, common_1.Get)('create-test-users'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "createTestUsers", null);
exports.AuthController = AuthController = __decorate([
    (0, common_1.Controller)('auth'),
    (0, common_1.UseGuards)(throttler_1.ThrottlerGuard),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
