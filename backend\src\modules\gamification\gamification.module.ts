import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GamificationService } from './gamification.service';
import { XPLog } from './xp-log.entity';
import { Badge } from './badge.entity';
import { Streak } from './streak.entity';
// ...other imports

@Module({
  imports: [
    TypeOrmModule.forFeature([XPLog, Badge, Streak]),
    // ...other modules
  ],
  providers: [GamificationService],
  exports: [GamificationService],
})
export class GamificationModule {} 