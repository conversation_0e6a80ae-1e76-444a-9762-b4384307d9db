"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var JwtStrategy_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_jwt_1 = require("passport-jwt");
const config_1 = require("@nestjs/config");
const users_service_1 = require("../users/users.service");
let JwtStrategy = JwtStrategy_1 = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    constructor(configService, usersService) {
        const jwtSecret = configService.get('JWT_SECRET');
        if (!jwtSecret) {
            const errorMsg = 'JWT_SECRET not found in environment variables';
            console.error(`[${JwtStrategy_1.name}] ${errorMsg}`);
            throw new Error('JWT_SECRET must be defined in environment variables');
        }
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: jwtSecret,
        });
        this.configService = configService;
        this.usersService = usersService;
        this.logger = new common_1.Logger(JwtStrategy_1.name);
        this.logger.log('JWT Strategy initialized successfully');
    }
    async validate(payload) {
        const logContext = `User ID: ${payload.sub}, Email: ${payload.email}`;
        try {
            this.logger.debug(`Validating token for ${logContext}`);
            // Validate payload structure
            this.validatePayloadStructure(payload);
            // Fetch user from database
            const user = await this.fetchAndValidateUser(payload);
            // Additional security checks
            this.performSecurityChecks(user, payload);
            this.logger.log(`User validated successfully: ${user.email}, Role: ${user.role}`);
            return {
                id: user.id,
                email: user.email,
                role: user.role,
                is_active: user.is_active ?? true,
            };
        }
        catch (error) {
            this.logger.error(`Token validation failed for ${logContext}: ${error.message}`);
            if (error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            // Log unexpected errors but don't expose internal details
            this.logger.error(`Unexpected error during token validation: ${error.stack}`);
            throw new common_1.UnauthorizedException('Authentication failed');
        }
    }
    validatePayloadStructure(payload) {
        if (!payload.sub) {
            this.logger.warn('Token payload missing user ID (sub field)');
            throw new common_1.UnauthorizedException('Invalid token format: missing user ID');
        }
        if (!payload.email) {
            this.logger.warn('Token payload missing email field');
            throw new common_1.UnauthorizedException('Invalid token format: missing email');
        }
        // Validate email format (basic check)
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(payload.email)) {
            this.logger.warn(`Invalid email format in token: ${payload.email}`);
            throw new common_1.UnauthorizedException('Invalid token format: malformed email');
        }
    }
    async fetchAndValidateUser(payload) {
        const user = await this.usersService.findById(payload.sub);
        if (!user) {
            this.logger.warn(`User not found for ID: ${payload.sub}`);
            throw new common_1.UnauthorizedException('User not found');
        }
        // Check if user account is active/enabled
        if (user.is_active === false) {
            this.logger.warn(`Inactive user attempted authentication: ${payload.sub}`);
            throw new common_1.UnauthorizedException('Account is inactive');
        }
        return user;
    }
    performSecurityChecks(user, payload) {
        // Verify email matches (protect against email changes after token issue)
        if (user.email !== payload.email) {
            this.logger.warn(`Email mismatch for user ID: ${payload.sub}. ` +
                `Token email: ${payload.email}, Current email: ${user.email}`);
            throw new common_1.UnauthorizedException('Token authentication failed');
        }
        // Optional: Check if user's role has changed significantly
        if (payload.role && user.role !== payload.role) {
            this.logger.warn(`Role mismatch detected for user ${payload.sub}. ` +
                `Token role: ${payload.role}, Current role: ${user.role}`);
            // Note: This might be expected behavior if roles can be updated
            // Consider whether to throw an error or just log the discrepancy
        }
        // Optional: Add timestamp checks for additional security
        if (payload.iat) {
            const tokenAge = Math.floor(Date.now() / 1000) - payload.iat;
            const maxTokenAge = this.configService.get('JWT_MAX_AGE_SECONDS', 86400); // 24 hours default
            if (tokenAge > maxTokenAge) {
                this.logger.warn(`Token too old for user ${payload.sub}: ${tokenAge} seconds`);
                throw new common_1.UnauthorizedException('Token has expired');
            }
        }
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = JwtStrategy_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        users_service_1.UsersService])
], JwtStrategy);
