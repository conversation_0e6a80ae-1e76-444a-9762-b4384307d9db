(()=>{var __webpack_modules__={"./dist/compiled/@edge-runtime/cookies/index.js":/*!******************************************************!*\
  !*** ./dist/compiled/@edge-runtime/cookies/index.js ***!
  \******************************************************/module1=>{"use strict";var __defProp=Object.defineProperty,__getOwnPropDesc=Object.getOwnPropertyDescriptor,__getOwnPropNames=Object.getOwnPropertyNames,__hasOwnProp=Object.prototype.hasOwnProperty,src_exports={};function stringifyCookie(c){var _a;let attrs=["path"in c&&c.path&&`Path=${c.path}`,"expires"in c&&(c.expires||0===c.expires)&&`Expires=${("number"==typeof c.expires?new Date(c.expires):c.expires).toUTCString()}`,"maxAge"in c&&"number"==typeof c.maxAge&&`Max-Age=${c.maxAge}`,"domain"in c&&c.domain&&`Domain=${c.domain}`,"secure"in c&&c.secure&&"Secure","httpOnly"in c&&c.httpOnly&&"HttpOnly","sameSite"in c&&c.sameSite&&`SameSite=${c.sameSite}`,"partitioned"in c&&c.partitioned&&"Partitioned","priority"in c&&c.priority&&`Priority=${c.priority}`].filter(Boolean),stringified=`${c.name}=${encodeURIComponent(null!=(_a=c.value)?_a:"")}`;return 0===attrs.length?stringified:`${stringified}; ${attrs.join("; ")}`}function parseCookie(cookie){let map=/* @__PURE__ */new Map;for(let pair of cookie.split(/; */)){if(!pair)continue;let splitAt=pair.indexOf("=");if(-1===splitAt){map.set(pair,"true");continue}let[key,value]=[pair.slice(0,splitAt),pair.slice(splitAt+1)];try{map.set(key,decodeURIComponent(null!=value?value:"true"))}catch{}}return map}function parseSetCookie(setCookie){var string,string1;if(!setCookie)return;let[[name,value],...attributes]=parseCookie(setCookie),{domain,expires,httponly,maxage,path,samesite,secure,partitioned,priority}=Object.fromEntries(attributes.map(([key,value2])=>[key.toLowerCase().replace(/-/g,""),value2]));return function(t){let newT={};for(let key in t)t[key]&&(newT[key]=t[key]);return newT}({name,value:decodeURIComponent(value),domain,...expires&&{expires:new Date(expires)},...httponly&&{httpOnly:!0},..."string"==typeof maxage&&{maxAge:Number(maxage)},path,...samesite&&{sameSite:SAME_SITE.includes(string=(string=samesite).toLowerCase())?string:void 0},...secure&&{secure:!0},...priority&&{priority:PRIORITY.includes(string1=(string1=priority).toLowerCase())?string1:void 0},...partitioned&&{partitioned:!0}})}((target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})})(src_exports,{RequestCookies:()=>RequestCookies,ResponseCookies:()=>ResponseCookies,parseCookie:()=>parseCookie,parseSetCookie:()=>parseSetCookie,stringifyCookie:()=>stringifyCookie}),module1.exports=((to,from,except,desc)=>{if(from&&"object"==typeof from||"function"==typeof from)for(let key of __getOwnPropNames(from))__hasOwnProp.call(to,key)||void 0===key||__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to})(__defProp({},"__esModule",{value:!0}),src_exports);var SAME_SITE=["strict","lax","none"],PRIORITY=["low","medium","high"],RequestCookies=class{constructor(requestHeaders){this._parsed=/* @__PURE__ */new Map,this._headers=requestHeaders;let header=requestHeaders.get("cookie");if(header)for(let[name,value]of parseCookie(header))this._parsed.set(name,{name,value})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...args){let name="string"==typeof args[0]?args[0]:args[0].name;return this._parsed.get(name)}getAll(...args){var _a;let all=Array.from(this._parsed);if(!args.length)return all.map(([_,value])=>value);let name="string"==typeof args[0]?args[0]:null==(_a=args[0])?void 0:_a.name;return all.filter(([n])=>n===name).map(([_,value])=>value)}has(name){return this._parsed.has(name)}set(...args){let[name,value]=1===args.length?[args[0].name,args[0].value]:args,map=this._parsed;return map.set(name,{name,value}),this._headers.set("cookie",Array.from(map).map(([_,value2])=>stringifyCookie(value2)).join("; ")),this}delete(names){let map=this._parsed,result=Array.isArray(names)?names.map(name=>map.delete(name)):map.delete(names);return this._headers.set("cookie",Array.from(map).map(([_,value])=>stringifyCookie(value)).join("; ")),result}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(v=>`${v.name}=${encodeURIComponent(v.value)}`).join("; ")}},ResponseCookies=class{constructor(responseHeaders){var _a,_b,_c;this._parsed=/* @__PURE__ */new Map,this._headers=responseHeaders;let setCookie=null!=(_c=null!=(_b=null==(_a=responseHeaders.getSetCookie)?void 0:_a.call(responseHeaders))?_b:responseHeaders.get("set-cookie"))?_c:[];for(let cookieString of Array.isArray(setCookie)?setCookie:function(cookiesString){if(!cookiesString)return[];var start,ch,lastComma,nextStart,cookiesSeparatorFound,cookiesStrings=[],pos=0;function skipWhitespace(){for(;pos<cookiesString.length&&/\s/.test(cookiesString.charAt(pos));)pos+=1;return pos<cookiesString.length}for(;pos<cookiesString.length;){for(start=pos,cookiesSeparatorFound=!1;skipWhitespace();)if(","===(ch=cookiesString.charAt(pos))){for(lastComma=pos,pos+=1,skipWhitespace(),nextStart=pos;pos<cookiesString.length&&"="!==(ch=cookiesString.charAt(pos))&&";"!==ch&&","!==ch;)pos+=1;pos<cookiesString.length&&"="===cookiesString.charAt(pos)?(cookiesSeparatorFound=!0,pos=nextStart,cookiesStrings.push(cookiesString.substring(start,lastComma)),start=pos):pos=lastComma+1}else pos+=1;(!cookiesSeparatorFound||pos>=cookiesString.length)&&cookiesStrings.push(cookiesString.substring(start,cookiesString.length))}return cookiesStrings}(setCookie)){let parsed=parseSetCookie(cookieString);parsed&&this._parsed.set(parsed.name,parsed)}}get(...args){let key="string"==typeof args[0]?args[0]:args[0].name;return this._parsed.get(key)}getAll(...args){var _a;let all=Array.from(this._parsed.values());if(!args.length)return all;let key="string"==typeof args[0]?args[0]:null==(_a=args[0])?void 0:_a.name;return all.filter(c=>c.name===key)}has(name){return this._parsed.has(name)}set(...args){let[name,value,cookie]=1===args.length?[args[0].name,args[0].value,args[0]]:args,map=this._parsed;return map.set(name,function(cookie={name:"",value:""}){return"number"==typeof cookie.expires&&(cookie.expires=new Date(cookie.expires)),cookie.maxAge&&(cookie.expires=new Date(Date.now()+1e3*cookie.maxAge)),(null===cookie.path||void 0===cookie.path)&&(cookie.path="/"),cookie}({name,value,...cookie})),function(bag,headers){for(let[,value]of(headers.delete("set-cookie"),bag)){let serialized=stringifyCookie(value);headers.append("set-cookie",serialized)}}(map,this._headers),this}delete(...args){let[name,options]="string"==typeof args[0]?[args[0]]:[args[0].name,args[0]];return this.set({...options,name,value:"",expires:/* @__PURE__ */new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(stringifyCookie).join("; ")}}},"./dist/compiled/bytes/index.js":/*!**************************************!*\
  !*** ./dist/compiled/bytes/index.js ***!
  \**************************************/module1=>{(()=>{"use strict";var e={56:e=>{/*!
 * bytes
 * Copyright(c) 2012-2014 TJ Holowaychuk
 * Copyright(c) 2015 Jed Watson
 * MIT Licensed
 */e.exports=function(e,r){return"string"==typeof e?parse(e):"number"==typeof e?format(e,r):null},e.exports.format=format,e.exports.parse=parse;var r=/\B(?=(\d{3})+(?!\d))/g,a=/(?:\.0*|(\.[^0]+)0+)$/,t={b:1,kb:1024,mb:1048576,gb:0x40000000,tb:0x10000000000,pb:0x4000000000000},i=/^((-|\+)?(\d+(?:\.\d+)?)) *(kb|mb|gb|tb|pb)$/i;function format(e,i){if(!Number.isFinite(e))return null;var n=Math.abs(e),o=i&&i.thousandsSeparator||"",s=i&&i.unitSeparator||"",f=i&&void 0!==i.decimalPlaces?i.decimalPlaces:2,u=!!(i&&i.fixedDecimals),p=i&&i.unit||"";p&&t[p.toLowerCase()]||(p=n>=t.pb?"PB":n>=t.tb?"TB":n>=t.gb?"GB":n>=t.mb?"MB":n>=t.kb?"KB":"B");var l=(e/t[p.toLowerCase()]).toFixed(f);return u||(l=l.replace(a,"$1")),o&&(l=l.split(".").map(function(e,a){return 0===a?e.replace(r,o):e}).join(".")),l+s+p}function parse(e){if("number"==typeof e&&!isNaN(e))return e;if("string"!=typeof e)return null;var a,r=i.exec(e),n="b";return r?(a=parseFloat(r[1]),n=r[4].toLowerCase()):(a=parseInt(e,10),n="b"),Math.floor(t[n]*a)}}},r={};function __nccwpck_require__1(a){var t=r[a];if(void 0!==t)return t.exports;var i=r[a]={exports:{}},n=!0;try{e[a](i,i.exports,__nccwpck_require__1),n=!1}finally{n&&delete r[a]}return i.exports}__nccwpck_require__1.ab=__dirname+"/";var a=__nccwpck_require__1(56);module1.exports=a})()},"./dist/compiled/content-type/index.js":/*!*********************************************!*\
  !*** ./dist/compiled/content-type/index.js ***!
  \*********************************************/module1=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var e={};(()=>{/*!
 * content-type
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var t=/; *([!#$%&'*+.^_`|~0-9A-Za-z-]+) *= *("(?:[\u000b\u0020\u0021\u0023-\u005b\u005d-\u007e\u0080-\u00ff]|\\[\u000b\u0020-\u00ff])*"|[!#$%&'*+.^_`|~0-9A-Za-z-]+) */g,a=/^[\u000b\u0020-\u007e\u0080-\u00ff]+$/,n=/^[!#$%&'*+.^_`|~0-9A-Za-z-]+$/,i=/\\([\u000b\u0020-\u00ff])/g,o=/([\\"])/g,f=/^[!#$%&'*+.^_`|~0-9A-Za-z-]+\/[!#$%&'*+.^_`|~0-9A-Za-z-]+$/;function ContentType(e){this.parameters=Object.create(null),this.type=e}e.format=function(e){if(!e||"object"!=typeof e)throw TypeError("argument obj is required");var r=e.parameters,t=e.type;if(!t||!f.test(t))throw TypeError("invalid type");var a1=t;if(r&&"object"==typeof r)for(var i,o1=Object.keys(r).sort(),u=0;u<o1.length;u++){if(i=o1[u],!n.test(i))throw TypeError("invalid parameter name");a1+="; "+i+"="+function(e){var r=String(e);if(n.test(r))return r;if(r.length>0&&!a.test(r))throw TypeError("invalid parameter value");return'"'+r.replace(o,"\\$1")+'"'}(r[i])}return a1},e.parse=function(e){if(!e)throw TypeError("argument string is required");var u,p,s,r="object"==typeof e?function(e){var r;if("function"==typeof e.getHeader?r=e.getHeader("content-type"):"object"==typeof e.headers&&(r=e.headers&&e.headers["content-type"]),"string"!=typeof r)throw TypeError("content-type header is missing from object");return r}(e):e;if("string"!=typeof r)throw TypeError("argument string is required to be a string");var a=r.indexOf(";"),n=-1!==a?r.substr(0,a).trim():r.trim();if(!f.test(n))throw TypeError("invalid media type");var o=new ContentType(n.toLowerCase());if(-1!==a){for(t.lastIndex=a;p=t.exec(r);){if(p.index!==a)throw TypeError("invalid parameter format");a+=p[0].length,u=p[1].toLowerCase(),'"'===(s=p[2])[0]&&(s=s.substr(1,s.length-2).replace(i,"$1")),o.parameters[u]=s}if(a!==r.length)throw TypeError("invalid parameter format")}return o}})(),module1.exports=e})()},"./dist/compiled/cookie/index.js":/*!***************************************!*\
  !*** ./dist/compiled/cookie/index.js ***!
  \***************************************/module1=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var e={};(()=>{/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */e.parse=function(e,r){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var t={},o=e.split(a),s=(r||{}).decode||i,p=0;p<o.length;p++){var f=o[p],u=f.indexOf("=");if(!(u<0)){var v=f.substr(0,u).trim(),c=f.substr(++u,f.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==t[v]&&(t[v]=function(e,r){try{return r(e)}catch(r){return e}}(c,s))}}return t},e.serialize=function(e,r,i){var a=i||{},o=a.encode||t;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var s=o(r);if(s&&!n.test(s))throw TypeError("argument val is invalid");var p=e+"="+s;if(null!=a.maxAge){var f=a.maxAge-0;if(isNaN(f)||!isFinite(f))throw TypeError("option maxAge is invalid");p+="; Max-Age="+Math.floor(f)}if(a.domain){if(!n.test(a.domain))throw TypeError("option domain is invalid");p+="; Domain="+a.domain}if(a.path){if(!n.test(a.path))throw TypeError("option path is invalid");p+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");p+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(p+="; HttpOnly"),a.secure&&(p+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":p+="; SameSite=Strict";break;case"lax":p+="; SameSite=Lax";break;case"none":p+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return p};var i=decodeURIComponent,t=encodeURIComponent,a=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),module1.exports=e})()},"./dist/compiled/fresh/index.js":/*!**************************************!*\
  !*** ./dist/compiled/fresh/index.js ***!
  \**************************************/module1=>{(()=>{"use strict";var e={695:e=>{/*!
 * fresh
 * Copyright(c) 2012 TJ Holowaychuk
 * Copyright(c) 2016-2017 Douglas Christopher Wilson
 * MIT Licensed
 */var r=/(?:^|,)\s*?no-cache\s*?(?:,|$)/;function parseHttpDate(e){var r=e&&Date.parse(e);return"number"==typeof r?r:NaN}e.exports=function(e,a){var t=e["if-modified-since"],s=e["if-none-match"];if(!t&&!s)return!1;var i=e["cache-control"];if(i&&r.test(i))return!1;if(s&&"*"!==s){var f=a.etag;if(!f)return!1;for(var n=!0,u=function(e){for(var r=0,a=[],t=0,s=0,i=e.length;s<i;s++)switch(e.charCodeAt(s)){case 32:t===r&&(t=r=s+1);break;case 44:a.push(e.substring(t,r)),t=r=s+1;break;default:r=s+1}return a.push(e.substring(t,r)),a}(s),_=0;_<u.length;_++){var o=u[_];if(o===f||o==="W/"+f||"W/"+o===f){n=!1;break}}if(n)return!1}if(t){var p=a["last-modified"];if(!p||!(parseHttpDate(p)<=parseHttpDate(t)))return!1}return!0}}},r={};function __nccwpck_require__1(a){var t=r[a];if(void 0!==t)return t.exports;var s=r[a]={exports:{}},i=!0;try{e[a](s,s.exports,__nccwpck_require__1),i=!1}finally{i&&delete r[a]}return s.exports}__nccwpck_require__1.ab=__dirname+"/";var a=__nccwpck_require__1(695);module1.exports=a})()},"./dist/esm/server/crypto-utils.js":/*!*****************************************************!*\
  !*** ./dist/esm/server/crypto-utils.js + 1 modules ***!
  \*****************************************************/(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{decryptWithSecret:()=>decryptWithSecret,encryptWithSecret:()=>encryptWithSecret});let external_crypto_namespaceObject=require("crypto");var external_crypto_default=/*#__PURE__*/__webpack_require__.n(external_crypto_namespaceObject);let CIPHER_ALGORITHM="aes-256-gcm";function encryptWithSecret(secret,data){let iv=external_crypto_default().randomBytes(16),salt=external_crypto_default().randomBytes(64),key=external_crypto_default().pbkdf2Sync(secret,salt,1e5,32,"sha512"),cipher=external_crypto_default().createCipheriv(CIPHER_ALGORITHM,key,iv),encrypted=Buffer.concat([cipher.update(data,"utf8"),cipher.final()]),tag=cipher.getAuthTag();return Buffer.concat([salt,iv,tag,encrypted]).toString("hex")}function decryptWithSecret(secret,encryptedData){let buffer=Buffer.from(encryptedData,"hex"),salt=buffer.slice(0,64),iv=buffer.slice(64,80),tag=buffer.slice(80,96),encrypted=buffer.slice(96),key=external_crypto_default().pbkdf2Sync(secret,salt,1e5,32,"sha512"),decipher=external_crypto_default().createDecipheriv(CIPHER_ALGORITHM,key,iv);return decipher.setAuthTag(tag),decipher.update(encrypted)+decipher.final("utf8")}},"next/dist/compiled/jsonwebtoken":/*!**************************************************!*\
  !*** external "next/dist/compiled/jsonwebtoken" ***!
  \**************************************************/module1=>{"use strict";module1.exports=require("next/dist/compiled/jsonwebtoken")},"next/dist/compiled/raw-body":/*!**********************************************!*\
  !*** external "next/dist/compiled/raw-body" ***!
  \**********************************************/module1=>{"use strict";module1.exports=require("next/dist/compiled/raw-body")},querystring:/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/module1=>{"use strict";module1.exports=require("querystring")}},__webpack_module_cache__={};function __webpack_require__(moduleId){var cachedModule=__webpack_module_cache__[moduleId];if(void 0!==cachedModule)return cachedModule.exports;var module1=__webpack_module_cache__[moduleId]={exports:{}};return __webpack_modules__[moduleId](module1,module1.exports,__webpack_require__),module1.exports}__webpack_require__.n=module1=>{var getter=module1&&module1.__esModule?()=>module1.default:()=>module1;return __webpack_require__.d(getter,{a:getter}),getter},__webpack_require__.d=(exports,definition)=>{for(var key in definition)__webpack_require__.o(definition,key)&&!__webpack_require__.o(exports,key)&&Object.defineProperty(exports,key,{enumerable:!0,get:definition[key]})},__webpack_require__.o=(obj,prop)=>Object.prototype.hasOwnProperty.call(obj,prop),__webpack_require__.r=exports=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(exports,"__esModule",{value:!0})};var __webpack_exports__={};(()=>{"use strict";/*!************************************************************************!*\
  !*** ./dist/esm/server/route-modules/pages-api/module.js + 21 modules ***!
  \************************************************************************/__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{PagesAPIRouteModule:()=>PagesAPIRouteModule,default:()=>pages_api_module});class ReflectAdapter{static get(target,prop,receiver){let value=Reflect.get(target,prop,receiver);return"function"==typeof value?value.bind(target):value}static set(target,prop,value,receiver){return Reflect.set(target,prop,value,receiver)}static has(target,prop){return Reflect.has(target,prop)}static deleteProperty(target,prop){return Reflect.deleteProperty(target,prop)}}class ReadonlyHeadersError extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new ReadonlyHeadersError}}class HeadersAdapter extends Headers{constructor(headers){super(),this.headers=new Proxy(headers,{get(target,prop,receiver){if("symbol"==typeof prop)return ReflectAdapter.get(target,prop,receiver);let lowercased=prop.toLowerCase(),original=Object.keys(headers).find(o=>o.toLowerCase()===lowercased);if(void 0!==original)return ReflectAdapter.get(target,original,receiver)},set(target,prop,value,receiver){if("symbol"==typeof prop)return ReflectAdapter.set(target,prop,value,receiver);let lowercased=prop.toLowerCase(),original=Object.keys(headers).find(o=>o.toLowerCase()===lowercased);return ReflectAdapter.set(target,original??prop,value,receiver)},has(target,prop){if("symbol"==typeof prop)return ReflectAdapter.has(target,prop);let lowercased=prop.toLowerCase(),original=Object.keys(headers).find(o=>o.toLowerCase()===lowercased);return void 0!==original&&ReflectAdapter.has(target,original)},deleteProperty(target,prop){if("symbol"==typeof prop)return ReflectAdapter.deleteProperty(target,prop);let lowercased=prop.toLowerCase(),original=Object.keys(headers).find(o=>o.toLowerCase()===lowercased);return void 0===original||ReflectAdapter.deleteProperty(target,original)}})}static seal(headers){return new Proxy(headers,{get(target,prop,receiver){switch(prop){case"append":case"delete":case"set":return ReadonlyHeadersError.callable;default:return ReflectAdapter.get(target,prop,receiver)}}})}merge(value){return Array.isArray(value)?value.join(", "):value}static from(headers){return headers instanceof Headers?headers:new HeadersAdapter(headers)}append(name,value){let existing=this.headers[name];"string"==typeof existing?this.headers[name]=[existing,value]:Array.isArray(existing)?existing.push(value):this.headers[name]=value}delete(name){delete this.headers[name]}get(name){let value=this.headers[name];return void 0!==value?this.merge(value):null}has(name){return void 0!==this.headers[name]}set(name,value){this.headers[name]=value}forEach(callbackfn,thisArg){for(let[name,value]of this.entries())callbackfn.call(thisArg,value,name,this)}*entries(){for(let key of Object.keys(this.headers)){let name=key.toLowerCase(),value=this.get(name);yield[name,value]}}*keys(){for(let key of Object.keys(this.headers)){let name=key.toLowerCase();yield name}}*values(){for(let key of Object.keys(this.headers)){let value=this.get(key);yield value}}[Symbol.iterator](){return this.entries()}}let PRERENDER_REVALIDATE_HEADER="x-prerender-revalidate",PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER="x-prerender-revalidate-if-generated",WEBPACK_LAYERS_NAMES={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};({...WEBPACK_LAYERS_NAMES,GROUP:{builtinReact:[WEBPACK_LAYERS_NAMES.reactServerComponents,WEBPACK_LAYERS_NAMES.actionBrowser],serverOnly:[WEBPACK_LAYERS_NAMES.reactServerComponents,WEBPACK_LAYERS_NAMES.actionBrowser,WEBPACK_LAYERS_NAMES.instrument,WEBPACK_LAYERS_NAMES.middleware],neutralTarget:[WEBPACK_LAYERS_NAMES.apiNode,WEBPACK_LAYERS_NAMES.apiEdge],clientOnly:[WEBPACK_LAYERS_NAMES.serverSideRendering,WEBPACK_LAYERS_NAMES.appPagesBrowser],bundled:[WEBPACK_LAYERS_NAMES.reactServerComponents,WEBPACK_LAYERS_NAMES.actionBrowser,WEBPACK_LAYERS_NAMES.serverSideRendering,WEBPACK_LAYERS_NAMES.appPagesBrowser,WEBPACK_LAYERS_NAMES.shared,WEBPACK_LAYERS_NAMES.instrument,WEBPACK_LAYERS_NAMES.middleware],appPages:[WEBPACK_LAYERS_NAMES.reactServerComponents,WEBPACK_LAYERS_NAMES.serverSideRendering,WEBPACK_LAYERS_NAMES.appPagesBrowser,WEBPACK_LAYERS_NAMES.actionBrowser]}});let tracer_namespaceObject=require("next/dist/server/lib/trace/tracer");var BaseServerSpan=/*#__PURE__*/function(BaseServerSpan){return BaseServerSpan.handleRequest="BaseServer.handleRequest",BaseServerSpan.run="BaseServer.run",BaseServerSpan.pipe="BaseServer.pipe",BaseServerSpan.getStaticHTML="BaseServer.getStaticHTML",BaseServerSpan.render="BaseServer.render",BaseServerSpan.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",BaseServerSpan.renderToResponse="BaseServer.renderToResponse",BaseServerSpan.renderToHTML="BaseServer.renderToHTML",BaseServerSpan.renderError="BaseServer.renderError",BaseServerSpan.renderErrorToResponse="BaseServer.renderErrorToResponse",BaseServerSpan.renderErrorToHTML="BaseServer.renderErrorToHTML",BaseServerSpan.render404="BaseServer.render404",BaseServerSpan}(BaseServerSpan||{}),LoadComponentsSpan=/*#__PURE__*/function(LoadComponentsSpan){return LoadComponentsSpan.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",LoadComponentsSpan.loadComponents="LoadComponents.loadComponents",LoadComponentsSpan}(LoadComponentsSpan||{}),NextServerSpan=/*#__PURE__*/function(NextServerSpan){return NextServerSpan.getRequestHandler="NextServer.getRequestHandler",NextServerSpan.getServer="NextServer.getServer",NextServerSpan.getServerRequestHandler="NextServer.getServerRequestHandler",NextServerSpan.createServer="createServer.createServer",NextServerSpan}(NextServerSpan||{}),NextNodeServerSpan=/*#__PURE__*/function(NextNodeServerSpan){return NextNodeServerSpan.compression="NextNodeServer.compression",NextNodeServerSpan.getBuildId="NextNodeServer.getBuildId",NextNodeServerSpan.createComponentTree="NextNodeServer.createComponentTree",NextNodeServerSpan.clientComponentLoading="NextNodeServer.clientComponentLoading",NextNodeServerSpan.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",NextNodeServerSpan.generateStaticRoutes="NextNodeServer.generateStaticRoutes",NextNodeServerSpan.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",NextNodeServerSpan.generatePublicRoutes="NextNodeServer.generatePublicRoutes",NextNodeServerSpan.generateImageRoutes="NextNodeServer.generateImageRoutes.route",NextNodeServerSpan.sendRenderResult="NextNodeServer.sendRenderResult",NextNodeServerSpan.proxyRequest="NextNodeServer.proxyRequest",NextNodeServerSpan.runApi="NextNodeServer.runApi",NextNodeServerSpan.render="NextNodeServer.render",NextNodeServerSpan.renderHTML="NextNodeServer.renderHTML",NextNodeServerSpan.imageOptimizer="NextNodeServer.imageOptimizer",NextNodeServerSpan.getPagePath="NextNodeServer.getPagePath",NextNodeServerSpan.getRoutesManifest="NextNodeServer.getRoutesManifest",NextNodeServerSpan.findPageComponents="NextNodeServer.findPageComponents",NextNodeServerSpan.getFontManifest="NextNodeServer.getFontManifest",NextNodeServerSpan.getServerComponentManifest="NextNodeServer.getServerComponentManifest",NextNodeServerSpan.getRequestHandler="NextNodeServer.getRequestHandler",NextNodeServerSpan.renderToHTML="NextNodeServer.renderToHTML",NextNodeServerSpan.renderError="NextNodeServer.renderError",NextNodeServerSpan.renderErrorToHTML="NextNodeServer.renderErrorToHTML",NextNodeServerSpan.render404="NextNodeServer.render404",NextNodeServerSpan.startResponse="NextNodeServer.startResponse",NextNodeServerSpan.route="route",NextNodeServerSpan.onProxyReq="onProxyReq",NextNodeServerSpan.apiResolver="apiResolver",NextNodeServerSpan.internalFetch="internalFetch",NextNodeServerSpan}(NextNodeServerSpan||{}),StartServerSpan=/*#__PURE__*/function(StartServerSpan){return StartServerSpan.startServer="startServer.startServer",StartServerSpan}(StartServerSpan||{}),RenderSpan=/*#__PURE__*/function(RenderSpan){return RenderSpan.getServerSideProps="Render.getServerSideProps",RenderSpan.getStaticProps="Render.getStaticProps",RenderSpan.renderToString="Render.renderToString",RenderSpan.renderDocument="Render.renderDocument",RenderSpan.createBodyResult="Render.createBodyResult",RenderSpan}(RenderSpan||{}),AppRenderSpan=/*#__PURE__*/function(AppRenderSpan){return AppRenderSpan.renderToString="AppRender.renderToString",AppRenderSpan.renderToReadableStream="AppRender.renderToReadableStream",AppRenderSpan.getBodyResult="AppRender.getBodyResult",AppRenderSpan.fetch="AppRender.fetch",AppRenderSpan}(AppRenderSpan||{}),RouterSpan=/*#__PURE__*/function(RouterSpan){return RouterSpan.executeRoute="Router.executeRoute",RouterSpan}(RouterSpan||{}),NodeSpan=/*#__PURE__*/function(NodeSpan){return NodeSpan.runHandler="Node.runHandler",NodeSpan}(NodeSpan||{}),AppRouteRouteHandlersSpan=/*#__PURE__*/function(AppRouteRouteHandlersSpan){return AppRouteRouteHandlersSpan.runHandler="AppRouteRouteHandlers.runHandler",AppRouteRouteHandlersSpan}(AppRouteRouteHandlersSpan||{}),ResolveMetadataSpan=/*#__PURE__*/function(ResolveMetadataSpan){return ResolveMetadataSpan.generateMetadata="ResolveMetadata.generateMetadata",ResolveMetadataSpan.generateViewport="ResolveMetadata.generateViewport",ResolveMetadataSpan}(ResolveMetadataSpan||{}),MiddlewareSpan=/*#__PURE__*/function(MiddlewareSpan){return MiddlewareSpan.execute="Middleware.execute",MiddlewareSpan}(MiddlewareSpan||{});let COOKIE_NAME_PRERENDER_BYPASS="__prerender_bypass",COOKIE_NAME_PRERENDER_DATA="__next_preview_data",SYMBOL_PREVIEW_DATA=Symbol(COOKIE_NAME_PRERENDER_DATA),SYMBOL_CLEARED_COOKIES=Symbol(COOKIE_NAME_PRERENDER_BYPASS);function clearPreviewData(res,options={}){if(SYMBOL_CLEARED_COOKIES in res)return res;let{serialize}=__webpack_require__(/*! next/dist/compiled/cookie */"./dist/compiled/cookie/index.js"),previous=res.getHeader("Set-Cookie");return res.setHeader("Set-Cookie",[..."string"==typeof previous?[previous]:Array.isArray(previous)?previous:[],serialize(COOKIE_NAME_PRERENDER_BYPASS,"",{expires:new Date(0),httpOnly:!0,sameSite:"lax",secure:!1,path:"/",...void 0!==options.path?{path:options.path}:void 0}),serialize(COOKIE_NAME_PRERENDER_DATA,"",{expires:new Date(0),httpOnly:!0,sameSite:"lax",secure:!1,path:"/",...void 0!==options.path?{path:options.path}:void 0})]),Object.defineProperty(res,SYMBOL_CLEARED_COOKIES,{value:!0,enumerable:!1}),res}class ApiError extends Error{constructor(statusCode,message){super(message),this.statusCode=statusCode}}function sendError(res,statusCode,message){res.statusCode=statusCode,res.statusMessage=message,res.end(message)}function setLazyProp({req},prop,getter){let opts={configurable:!0,enumerable:!0},optsReset={...opts,writable:!0};Object.defineProperty(req,prop,{...opts,get:()=>{let value=getter();return Object.defineProperty(req,prop,{...optsReset,value}),value},set:value=>{Object.defineProperty(req,prop,{...optsReset,value})}})}class RouteModule{constructor({userland,definition}){this.userland=userland,this.definition=definition}}var bytes=__webpack_require__("./dist/compiled/bytes/index.js"),bytes_default=/*#__PURE__*/__webpack_require__.n(bytes);let fnv1a52=str=>{let len=str.length,i=0,t0=0,v0=8997,t1=0,v1=33826,t2=0,v2=40164,t3=0,v3=52210;for(;i<len;)v0^=str.charCodeAt(i++),t0=435*v0,t1=435*v1,t2=435*v2,t3=435*v3,t2+=v0<<8,t3+=v1<<8,t1+=t0>>>16,v0=65535&t0,t2+=t1>>>16,v1=65535&t1,v3=t3+(t2>>>16)&65535,v2=65535&t2;return(15&v3)*0x1000000000000+0x100000000*v2+65536*v1+(v0^v3>>4)},generateETag=(payload,weak=!1)=>(weak?'W/"':'"')+fnv1a52(payload).toString(36)+payload.length.toString(36)+'"';"undefined"!=typeof performance&&["mark","measure","getEntriesByName"].every(method=>"function"==typeof performance[method]);var fresh=__webpack_require__("./dist/compiled/fresh/index.js"),fresh_default=/*#__PURE__*/__webpack_require__.n(fresh);let external_stream_namespaceObject=require("stream");function isError(err){return"object"==typeof err&&null!==err&&"name"in err&&"message"in err}var _edge_runtime_cookies=__webpack_require__("./dist/compiled/@edge-runtime/cookies/index.js"),content_type=__webpack_require__("./dist/compiled/content-type/index.js");async function parseBody(req,limit){let contentType,buffer;try{contentType=(0,content_type.parse)(req.headers["content-type"]||"text/plain")}catch{contentType=(0,content_type.parse)("text/plain")}let{type,parameters}=contentType,encoding=parameters.charset||"utf-8";try{let getRawBody=__webpack_require__(/*! next/dist/compiled/raw-body */"next/dist/compiled/raw-body");buffer=await getRawBody(req,{encoding,limit})}catch(e){if(isError(e)&&"entity.too.large"===e.type)throw Object.defineProperty(new ApiError(413,`Body exceeded ${limit} limit`),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw Object.defineProperty(new ApiError(400,"Invalid body"),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}let body=buffer.toString();return"application/json"===type||"application/ld+json"===type?function(str){if(0===str.length)return{};try{return JSON.parse(str)}catch(e){throw Object.defineProperty(new ApiError(400,"Invalid JSON"),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}(body):"application/x-www-form-urlencoded"===type?__webpack_require__(/*! querystring */"querystring").decode(body):body}function isValidData(str){return"string"==typeof str&&str.length>=16}async function revalidate(urlPath,opts,req,context){if("string"!=typeof urlPath||!urlPath.startsWith("/"))throw Object.defineProperty(Error(`Invalid urlPath provided to revalidate(), must be a path e.g. /blog/post-1, received ${urlPath}`),"__NEXT_ERROR_CODE",{value:"E153",enumerable:!1,configurable:!0});let revalidateHeaders={[PRERENDER_REVALIDATE_HEADER]:context.previewModeId,...opts.unstable_onlyGenerated?{[PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER]:"1"}:{}},allowedRevalidateHeaderKeys=[...context.allowedRevalidateHeaderKeys||[]];for(let key of((context.trustHostHeader||context.dev)&&allowedRevalidateHeaderKeys.push("cookie"),context.trustHostHeader&&allowedRevalidateHeaderKeys.push("x-vercel-protection-bypass"),Object.keys(req.headers)))allowedRevalidateHeaderKeys.includes(key)&&(revalidateHeaders[key]=req.headers[key]);try{if(context.trustHostHeader){let res=await fetch(`https://${req.headers.host}${urlPath}`,{method:"HEAD",headers:revalidateHeaders}),cacheHeader=res.headers.get("x-vercel-cache")||res.headers.get("x-nextjs-cache");if((null==cacheHeader?void 0:cacheHeader.toUpperCase())!=="REVALIDATED"&&200!==res.status&&!(404===res.status&&opts.unstable_onlyGenerated))throw Object.defineProperty(Error(`Invalid response ${res.status}`),"__NEXT_ERROR_CODE",{value:"E175",enumerable:!1,configurable:!0})}else if(context.revalidate)await context.revalidate({urlPath,revalidateHeaders,opts});else throw Object.defineProperty(Error("Invariant: required internal revalidate method not passed to api-utils"),"__NEXT_ERROR_CODE",{value:"E174",enumerable:!1,configurable:!0})}catch(err){throw Object.defineProperty(Error(`Failed to revalidate ${urlPath}: ${isError(err)?err.message:err}`),"__NEXT_ERROR_CODE",{value:"E240",enumerable:!1,configurable:!0})}}async function apiResolver(req,res,query,resolverModule,apiContext,propagateError,dev,page,onError){try{var _config_api,_config_api1,_config_api2,headers;if(!resolverModule){res.statusCode=404,res.end("Not Found");return}let config=resolverModule.config||{},bodyParser=(null==(_config_api=config.api)?void 0:_config_api.bodyParser)!==!1,responseLimit=(null==(_config_api1=config.api)?void 0:_config_api1.responseLimit)??!0,externalResolver=(null==(_config_api2=config.api)?void 0:_config_api2.externalResolver)||!1;setLazyProp({req:req},"cookies",(headers=req.headers,function(){let{cookie}=headers;if(!cookie)return{};let{parse:parseCookieFn}=__webpack_require__(/*! next/dist/compiled/cookie */"./dist/compiled/cookie/index.js");return parseCookieFn(Array.isArray(cookie)?cookie.join("; "):cookie)})),req.query=query,setLazyProp({req:req},"previewData",()=>(function(req,res,options,multiZoneDraftMode){var _cookies_get,_cookies_get1;let encryptedPreviewData;if(options&&function(req,previewProps){let headers=HeadersAdapter.from(req.headers);return{isOnDemandRevalidate:headers.get(PRERENDER_REVALIDATE_HEADER)===previewProps.previewModeId,revalidateOnlyGenerated:headers.has(PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER)}}(req,options).isOnDemandRevalidate)return!1;if(SYMBOL_PREVIEW_DATA in req)return req[SYMBOL_PREVIEW_DATA];let headers=HeadersAdapter.from(req.headers),cookies=new _edge_runtime_cookies.RequestCookies(headers),previewModeId=null==(_cookies_get=cookies.get(COOKIE_NAME_PRERENDER_BYPASS))?void 0:_cookies_get.value,tokenPreviewData=null==(_cookies_get1=cookies.get(COOKIE_NAME_PRERENDER_DATA))?void 0:_cookies_get1.value;if(previewModeId&&!tokenPreviewData&&previewModeId===options.previewModeId){let data={};return Object.defineProperty(req,SYMBOL_PREVIEW_DATA,{value:data,enumerable:!1}),data}if(!previewModeId&&!tokenPreviewData)return!1;if(!previewModeId||!tokenPreviewData||previewModeId!==options.previewModeId)return multiZoneDraftMode||clearPreviewData(res),!1;try{encryptedPreviewData=__webpack_require__(/*! next/dist/compiled/jsonwebtoken */"next/dist/compiled/jsonwebtoken").verify(tokenPreviewData,options.previewModeSigningKey)}catch{return clearPreviewData(res),!1}let{decryptWithSecret}=__webpack_require__(/*! ../../crypto-utils */"./dist/esm/server/crypto-utils.js"),decryptedPreviewData=decryptWithSecret(Buffer.from(options.previewModeEncryptionKey),encryptedPreviewData.data);try{let data=JSON.parse(decryptedPreviewData);return Object.defineProperty(req,SYMBOL_PREVIEW_DATA,{value:data,enumerable:!1}),data}catch{return!1}})(req,res,apiContext,!!apiContext.multiZoneDraftMode)),setLazyProp({req:req},"preview",()=>!1!==req.previewData||void 0),setLazyProp({req:req},"draftMode",()=>req.preview),bodyParser&&!req.body&&(req.body=await parseBody(req,config.api&&config.api.bodyParser&&config.api.bodyParser.sizeLimit?config.api.bodyParser.sizeLimit:"1mb"));let contentLength=0,maxContentLength=responseLimit&&"boolean"!=typeof responseLimit?bytes_default().parse(responseLimit):4194304,writeData=res.write,endResponse=res.end;res.write=(...args)=>(contentLength+=Buffer.byteLength(args[0]||""),writeData.apply(res,args)),res.end=(...args)=>(args.length&&"function"!=typeof args[0]&&(contentLength+=Buffer.byteLength(args[0]||"")),responseLimit&&contentLength>=maxContentLength&&console.warn(`API response for ${req.url} exceeds ${bytes_default().format(maxContentLength)}. API Routes are meant to respond quickly. https://nextjs.org/docs/messages/api-routes-response-size-limit`),endResponse.apply(res,args)),res.status=statusCode=>(res.statusCode=statusCode,res),res.send=data=>(function(req,res,body){var etag;if(null==body){res.end();return}if(204===res.statusCode||304===res.statusCode){res.removeHeader("Content-Type"),res.removeHeader("Content-Length"),res.removeHeader("Transfer-Encoding"),body&&console.warn(`A body was attempted to be set with a 204 statusCode for ${req.url}, this is invalid and the body was ignored.
See more info here https://nextjs.org/docs/messages/invalid-api-status-body`),res.end();return}let contentType=res.getHeader("Content-Type");if(body instanceof external_stream_namespaceObject.Stream){contentType||res.setHeader("Content-Type","application/octet-stream"),body.pipe(res);return}let isJSONLike=["object","number","boolean"].includes(typeof body),stringifiedBody=isJSONLike?JSON.stringify(body):body;if((etag=generateETag(stringifiedBody))&&res.setHeader("ETag",etag),!fresh_default()(req.headers,{etag})||(res.statusCode=304,res.end(),0)){if(Buffer.isBuffer(body)){contentType||res.setHeader("Content-Type","application/octet-stream"),res.setHeader("Content-Length",body.length),res.end(body);return}isJSONLike&&res.setHeader("Content-Type","application/json; charset=utf-8"),res.setHeader("Content-Length",Buffer.byteLength(stringifiedBody)),res.end(stringifiedBody)}})(req,res,data),res.json=data=>{res.setHeader("Content-Type","application/json; charset=utf-8"),res.send(JSON.stringify(data))},res.redirect=(statusOrUrl,url)=>(function(res,statusOrUrl,url){if("string"==typeof statusOrUrl&&(url=statusOrUrl,statusOrUrl=307),"number"!=typeof statusOrUrl||"string"!=typeof url)throw Object.defineProperty(Error("Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination')."),"__NEXT_ERROR_CODE",{value:"E389",enumerable:!1,configurable:!0});return res.writeHead(statusOrUrl,{Location:url}),res.write(url),res.end(),res})(res,statusOrUrl,url),res.setDraftMode=(options={enable:!0})=>(function(res,options){if(!isValidData(options.previewModeId))throw Object.defineProperty(Error("invariant: invalid previewModeId"),"__NEXT_ERROR_CODE",{value:"E169",enumerable:!1,configurable:!0});let expires=options.enable?void 0:new Date(0),{serialize}=__webpack_require__(/*! next/dist/compiled/cookie */"./dist/compiled/cookie/index.js"),previous=res.getHeader("Set-Cookie");return res.setHeader("Set-Cookie",[..."string"==typeof previous?[previous]:Array.isArray(previous)?previous:[],serialize(COOKIE_NAME_PRERENDER_BYPASS,options.previewModeId,{httpOnly:!0,sameSite:"lax",secure:!1,path:"/",expires})]),res})(res,Object.assign({},apiContext,options)),res.setPreviewData=(data,options={})=>(function(res,data,options){if(!isValidData(options.previewModeId))throw Object.defineProperty(Error("invariant: invalid previewModeId"),"__NEXT_ERROR_CODE",{value:"E169",enumerable:!1,configurable:!0});if(!isValidData(options.previewModeEncryptionKey))throw Object.defineProperty(Error("invariant: invalid previewModeEncryptionKey"),"__NEXT_ERROR_CODE",{value:"E334",enumerable:!1,configurable:!0});if(!isValidData(options.previewModeSigningKey))throw Object.defineProperty(Error("invariant: invalid previewModeSigningKey"),"__NEXT_ERROR_CODE",{value:"E436",enumerable:!1,configurable:!0});let jsonwebtoken=__webpack_require__(/*! next/dist/compiled/jsonwebtoken */"next/dist/compiled/jsonwebtoken"),{encryptWithSecret}=__webpack_require__(/*! ../../crypto-utils */"./dist/esm/server/crypto-utils.js"),payload=jsonwebtoken.sign({data:encryptWithSecret(Buffer.from(options.previewModeEncryptionKey),JSON.stringify(data))},options.previewModeSigningKey,{algorithm:"HS256",...void 0!==options.maxAge?{expiresIn:options.maxAge}:void 0});if(payload.length>2048)throw Object.defineProperty(Error("Preview data is limited to 2KB currently, reduce how much data you are storing as preview data to continue"),"__NEXT_ERROR_CODE",{value:"E465",enumerable:!1,configurable:!0});let{serialize}=__webpack_require__(/*! next/dist/compiled/cookie */"./dist/compiled/cookie/index.js"),previous=res.getHeader("Set-Cookie");return res.setHeader("Set-Cookie",[..."string"==typeof previous?[previous]:Array.isArray(previous)?previous:[],serialize(COOKIE_NAME_PRERENDER_BYPASS,options.previewModeId,{httpOnly:!0,sameSite:"lax",secure:!1,path:"/",...void 0!==options.maxAge?{maxAge:options.maxAge}:void 0,...void 0!==options.path?{path:options.path}:void 0}),serialize(COOKIE_NAME_PRERENDER_DATA,payload,{httpOnly:!0,sameSite:"lax",secure:!1,path:"/",...void 0!==options.maxAge?{maxAge:options.maxAge}:void 0,...void 0!==options.path?{path:options.path}:void 0})]),res})(res,data,Object.assign({},apiContext,options)),res.clearPreviewData=(options={})=>clearPreviewData(res,options),res.revalidate=(urlPath,opts)=>revalidate(urlPath,opts||{},req,apiContext);let resolver=resolverModule.default||resolverModule,wasPiped=!1;res.once("pipe",()=>wasPiped=!0);let apiRouteResult=await resolver(req,res);if(void 0!==apiRouteResult){if(apiRouteResult instanceof Response)throw Object.defineProperty(Error('API route returned a Response object in the Node.js runtime, this is not supported. Please use `runtime: "edge"` instead: https://nextjs.org/docs/api-routes/edge-api-routes'),"__NEXT_ERROR_CODE",{value:"E36",enumerable:!1,configurable:!0});console.warn(`API handler should not return a value, received ${typeof apiRouteResult}.`)}externalResolver||res.finished||res.headersSent||wasPiped||console.warn(`API resolved without sending a response for ${req.url}, this may result in stalled requests.`)}catch(err){if(null==onError||onError(err,req,{routerKind:"Pages Router",routePath:page||"",routeType:"route",revalidateReason:void 0}),err instanceof ApiError)sendError(res,err.statusCode,err.message);else{if(dev)throw isError(err)&&(err.page=page),err;if(console.error(err),propagateError)throw err;sendError(res,500,"Internal Server Error")}}}class PagesAPIRouteModule extends RouteModule{constructor(options){if(super(options),"function"!=typeof options.userland.default)throw Object.defineProperty(Error(`Page ${options.definition.page} does not export a default function.`),"__NEXT_ERROR_CODE",{value:"E379",enumerable:!1,configurable:!0});this.apiResolverWrapped=function(page,handler){return(...args)=>((0,tracer_namespaceObject.getTracer)().setRootSpanAttribute("next.route",page),(0,tracer_namespaceObject.getTracer)().trace(NodeSpan.runHandler,{spanName:`executing api route (pages) ${page}`},()=>handler(...args)))}(options.definition.page,apiResolver)}async render(req,res,context){let{apiResolverWrapped}=this;await apiResolverWrapped(req,res,context.query,this.userland,{...context.previewProps,revalidate:context.revalidate,trustHostHeader:context.trustHostHeader,allowedRevalidateHeaderKeys:context.allowedRevalidateHeaderKeys,hostname:context.hostname,multiZoneDraftMode:context.multiZoneDraftMode,dev:context.dev},context.minimalMode,context.dev,context.page,context.onError)}}let pages_api_module=PagesAPIRouteModule})(),module.exports=__webpack_exports__})();
//# sourceMappingURL=pages-api.runtime.dev.js.map