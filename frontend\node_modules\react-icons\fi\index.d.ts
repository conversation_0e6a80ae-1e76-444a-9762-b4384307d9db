// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const FiActivity: IconType;
export declare const FiAirplay: IconType;
export declare const FiAlertCircle: IconType;
export declare const FiAlertOctagon: IconType;
export declare const FiAlertTriangle: IconType;
export declare const FiAlignCenter: IconType;
export declare const FiAlignJustify: IconType;
export declare const FiAlignLeft: IconType;
export declare const FiAlignRight: IconType;
export declare const FiAnchor: IconType;
export declare const FiAperture: IconType;
export declare const FiArchive: IconType;
export declare const FiArrowDownCircle: IconType;
export declare const FiArrowDownLeft: IconType;
export declare const FiArrowDownRight: IconType;
export declare const FiArrowDown: IconType;
export declare const FiArrowLeftCircle: IconType;
export declare const FiArrowLeft: IconType;
export declare const FiArrowRightCircle: IconType;
export declare const FiArrowRight: IconType;
export declare const FiArrowUpCircle: IconType;
export declare const FiArrowUpLeft: IconType;
export declare const FiArrowUpRight: IconType;
export declare const FiArrowUp: IconType;
export declare const FiAtSign: IconType;
export declare const FiAward: IconType;
export declare const FiBarChart2: IconType;
export declare const FiBarChart: IconType;
export declare const FiBatteryCharging: IconType;
export declare const FiBattery: IconType;
export declare const FiBellOff: IconType;
export declare const FiBell: IconType;
export declare const FiBluetooth: IconType;
export declare const FiBold: IconType;
export declare const FiBookOpen: IconType;
export declare const FiBook: IconType;
export declare const FiBookmark: IconType;
export declare const FiBox: IconType;
export declare const FiBriefcase: IconType;
export declare const FiCalendar: IconType;
export declare const FiCameraOff: IconType;
export declare const FiCamera: IconType;
export declare const FiCast: IconType;
export declare const FiCheckCircle: IconType;
export declare const FiCheckSquare: IconType;
export declare const FiCheck: IconType;
export declare const FiChevronDown: IconType;
export declare const FiChevronLeft: IconType;
export declare const FiChevronRight: IconType;
export declare const FiChevronUp: IconType;
export declare const FiChevronsDown: IconType;
export declare const FiChevronsLeft: IconType;
export declare const FiChevronsRight: IconType;
export declare const FiChevronsUp: IconType;
export declare const FiChrome: IconType;
export declare const FiCircle: IconType;
export declare const FiClipboard: IconType;
export declare const FiClock: IconType;
export declare const FiCloudDrizzle: IconType;
export declare const FiCloudLightning: IconType;
export declare const FiCloudOff: IconType;
export declare const FiCloudRain: IconType;
export declare const FiCloudSnow: IconType;
export declare const FiCloud: IconType;
export declare const FiCode: IconType;
export declare const FiCodepen: IconType;
export declare const FiCodesandbox: IconType;
export declare const FiCoffee: IconType;
export declare const FiColumns: IconType;
export declare const FiCommand: IconType;
export declare const FiCompass: IconType;
export declare const FiCopy: IconType;
export declare const FiCornerDownLeft: IconType;
export declare const FiCornerDownRight: IconType;
export declare const FiCornerLeftDown: IconType;
export declare const FiCornerLeftUp: IconType;
export declare const FiCornerRightDown: IconType;
export declare const FiCornerRightUp: IconType;
export declare const FiCornerUpLeft: IconType;
export declare const FiCornerUpRight: IconType;
export declare const FiCpu: IconType;
export declare const FiCreditCard: IconType;
export declare const FiCrop: IconType;
export declare const FiCrosshair: IconType;
export declare const FiDatabase: IconType;
export declare const FiDelete: IconType;
export declare const FiDisc: IconType;
export declare const FiDivideCircle: IconType;
export declare const FiDivideSquare: IconType;
export declare const FiDivide: IconType;
export declare const FiDollarSign: IconType;
export declare const FiDownloadCloud: IconType;
export declare const FiDownload: IconType;
export declare const FiDribbble: IconType;
export declare const FiDroplet: IconType;
export declare const FiEdit2: IconType;
export declare const FiEdit3: IconType;
export declare const FiEdit: IconType;
export declare const FiExternalLink: IconType;
export declare const FiEyeOff: IconType;
export declare const FiEye: IconType;
export declare const FiFacebook: IconType;
export declare const FiFastForward: IconType;
export declare const FiFeather: IconType;
export declare const FiFigma: IconType;
export declare const FiFileMinus: IconType;
export declare const FiFilePlus: IconType;
export declare const FiFileText: IconType;
export declare const FiFile: IconType;
export declare const FiFilm: IconType;
export declare const FiFilter: IconType;
export declare const FiFlag: IconType;
export declare const FiFolderMinus: IconType;
export declare const FiFolderPlus: IconType;
export declare const FiFolder: IconType;
export declare const FiFramer: IconType;
export declare const FiFrown: IconType;
export declare const FiGift: IconType;
export declare const FiGitBranch: IconType;
export declare const FiGitCommit: IconType;
export declare const FiGitMerge: IconType;
export declare const FiGitPullRequest: IconType;
export declare const FiGithub: IconType;
export declare const FiGitlab: IconType;
export declare const FiGlobe: IconType;
export declare const FiGrid: IconType;
export declare const FiHardDrive: IconType;
export declare const FiHash: IconType;
export declare const FiHeadphones: IconType;
export declare const FiHeart: IconType;
export declare const FiHelpCircle: IconType;
export declare const FiHexagon: IconType;
export declare const FiHome: IconType;
export declare const FiImage: IconType;
export declare const FiInbox: IconType;
export declare const FiInfo: IconType;
export declare const FiInstagram: IconType;
export declare const FiItalic: IconType;
export declare const FiKey: IconType;
export declare const FiLayers: IconType;
export declare const FiLayout: IconType;
export declare const FiLifeBuoy: IconType;
export declare const FiLink2: IconType;
export declare const FiLink: IconType;
export declare const FiLinkedin: IconType;
export declare const FiList: IconType;
export declare const FiLoader: IconType;
export declare const FiLock: IconType;
export declare const FiLogIn: IconType;
export declare const FiLogOut: IconType;
export declare const FiMail: IconType;
export declare const FiMapPin: IconType;
export declare const FiMap: IconType;
export declare const FiMaximize2: IconType;
export declare const FiMaximize: IconType;
export declare const FiMeh: IconType;
export declare const FiMenu: IconType;
export declare const FiMessageCircle: IconType;
export declare const FiMessageSquare: IconType;
export declare const FiMicOff: IconType;
export declare const FiMic: IconType;
export declare const FiMinimize2: IconType;
export declare const FiMinimize: IconType;
export declare const FiMinusCircle: IconType;
export declare const FiMinusSquare: IconType;
export declare const FiMinus: IconType;
export declare const FiMonitor: IconType;
export declare const FiMoon: IconType;
export declare const FiMoreHorizontal: IconType;
export declare const FiMoreVertical: IconType;
export declare const FiMousePointer: IconType;
export declare const FiMove: IconType;
export declare const FiMusic: IconType;
export declare const FiNavigation2: IconType;
export declare const FiNavigation: IconType;
export declare const FiOctagon: IconType;
export declare const FiPackage: IconType;
export declare const FiPaperclip: IconType;
export declare const FiPauseCircle: IconType;
export declare const FiPause: IconType;
export declare const FiPenTool: IconType;
export declare const FiPercent: IconType;
export declare const FiPhoneCall: IconType;
export declare const FiPhoneForwarded: IconType;
export declare const FiPhoneIncoming: IconType;
export declare const FiPhoneMissed: IconType;
export declare const FiPhoneOff: IconType;
export declare const FiPhoneOutgoing: IconType;
export declare const FiPhone: IconType;
export declare const FiPieChart: IconType;
export declare const FiPlayCircle: IconType;
export declare const FiPlay: IconType;
export declare const FiPlusCircle: IconType;
export declare const FiPlusSquare: IconType;
export declare const FiPlus: IconType;
export declare const FiPocket: IconType;
export declare const FiPower: IconType;
export declare const FiPrinter: IconType;
export declare const FiRadio: IconType;
export declare const FiRefreshCcw: IconType;
export declare const FiRefreshCw: IconType;
export declare const FiRepeat: IconType;
export declare const FiRewind: IconType;
export declare const FiRotateCcw: IconType;
export declare const FiRotateCw: IconType;
export declare const FiRss: IconType;
export declare const FiSave: IconType;
export declare const FiScissors: IconType;
export declare const FiSearch: IconType;
export declare const FiSend: IconType;
export declare const FiServer: IconType;
export declare const FiSettings: IconType;
export declare const FiShare2: IconType;
export declare const FiShare: IconType;
export declare const FiShieldOff: IconType;
export declare const FiShield: IconType;
export declare const FiShoppingBag: IconType;
export declare const FiShoppingCart: IconType;
export declare const FiShuffle: IconType;
export declare const FiSidebar: IconType;
export declare const FiSkipBack: IconType;
export declare const FiSkipForward: IconType;
export declare const FiSlack: IconType;
export declare const FiSlash: IconType;
export declare const FiSliders: IconType;
export declare const FiSmartphone: IconType;
export declare const FiSmile: IconType;
export declare const FiSpeaker: IconType;
export declare const FiSquare: IconType;
export declare const FiStar: IconType;
export declare const FiStopCircle: IconType;
export declare const FiSun: IconType;
export declare const FiSunrise: IconType;
export declare const FiSunset: IconType;
export declare const FiTable: IconType;
export declare const FiTablet: IconType;
export declare const FiTag: IconType;
export declare const FiTarget: IconType;
export declare const FiTerminal: IconType;
export declare const FiThermometer: IconType;
export declare const FiThumbsDown: IconType;
export declare const FiThumbsUp: IconType;
export declare const FiToggleLeft: IconType;
export declare const FiToggleRight: IconType;
export declare const FiTool: IconType;
export declare const FiTrash2: IconType;
export declare const FiTrash: IconType;
export declare const FiTrello: IconType;
export declare const FiTrendingDown: IconType;
export declare const FiTrendingUp: IconType;
export declare const FiTriangle: IconType;
export declare const FiTruck: IconType;
export declare const FiTv: IconType;
export declare const FiTwitch: IconType;
export declare const FiTwitter: IconType;
export declare const FiType: IconType;
export declare const FiUmbrella: IconType;
export declare const FiUnderline: IconType;
export declare const FiUnlock: IconType;
export declare const FiUploadCloud: IconType;
export declare const FiUpload: IconType;
export declare const FiUserCheck: IconType;
export declare const FiUserMinus: IconType;
export declare const FiUserPlus: IconType;
export declare const FiUserX: IconType;
export declare const FiUser: IconType;
export declare const FiUsers: IconType;
export declare const FiVideoOff: IconType;
export declare const FiVideo: IconType;
export declare const FiVoicemail: IconType;
export declare const FiVolume1: IconType;
export declare const FiVolume2: IconType;
export declare const FiVolumeX: IconType;
export declare const FiVolume: IconType;
export declare const FiWatch: IconType;
export declare const FiWifiOff: IconType;
export declare const FiWifi: IconType;
export declare const FiWind: IconType;
export declare const FiXCircle: IconType;
export declare const FiXOctagon: IconType;
export declare const FiXSquare: IconType;
export declare const FiX: IconType;
export declare const FiYoutube: IconType;
export declare const FiZapOff: IconType;
export declare const FiZap: IconType;
export declare const FiZoomIn: IconType;
export declare const FiZoomOut: IconType;
