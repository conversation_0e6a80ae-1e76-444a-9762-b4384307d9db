import { NestFactory } from '@nestjs/core';
import { <PERSON><PERSON><PERSON>, Controller, Get } from '@nestjs/common';

@Controller()
export class TestController {
  @Get()
  getHello(): string {
    return 'Hello World!';
  }

  @Get('health')
  getHealth(): object {
    return { status: 'ok', timestamp: new Date().toISOString() };
  }
}

@Module({
  controllers: [TestController],
})
export class TestModule {}

async function bootstrap() {
  console.log('Starting minimal test server...');
  
  try {
    const app = await NestFactory.create(TestModule, {
      logger: ['error', 'warn', 'log'],
    });

    const port = 3002;
    await app.listen(port);
    
    console.log(`✅ Test server is running on http://localhost:${port}`);
    console.log(`✅ Health check: http://localhost:${port}/health`);
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

bootstrap();
