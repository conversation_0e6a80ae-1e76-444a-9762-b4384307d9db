import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { XPLog } from './xp-log.entity';
import { Badge } from './badge.entity';
import { Streak } from './streak.entity';
// ...other imports

@Injectable()
export class GamificationService {
  constructor(
    @InjectRepository(XPLog)
    private xpLogRepo: Repository<XPLog>,
    @InjectRepository(Badge)
    private badgeRepo: Repository<Badge>,
    @InjectRepository(Streak)
    private streakRepo: Repository<Streak>,
    // ...other injections
  ) {}
  // ...existing methods
} 