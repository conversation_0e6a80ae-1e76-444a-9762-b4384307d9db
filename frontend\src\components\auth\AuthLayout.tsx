import { ReactNode } from 'react';
import Link from 'next/link';
import { useTheme } from '../../app/providers';
import { Sun, Moon, Stethoscope } from 'lucide-react';

interface AuthLayoutProps {
  children: ReactNode;
  title: string;
  description?: string;
  showThemeToggle?: boolean;
}

export default function AuthLayout({ 
  children, 
  title, 
  description = "Secure authentication for your medical education platform",
  showThemeToggle = true 
}: AuthLayoutProps) {
  const { theme, setTheme } = useTheme();

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex flex-col">
        {/* Header */}
        <header className="w-full p-4 sm:p-6">
          <div className="max-w-md mx-auto flex items-center justify-between">
            <Link 
              href="/" 
              className="flex items-center space-x-2 text-gray-900 dark:text-white hover:opacity-80 transition-opacity"
            >
              <Stethoscope className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <span className="text-xl font-bold">MedTrack</span>
            </Link>
            
            {showThemeToggle && (
              <button
                type="button"
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                className="p-2 rounded-lg bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                aria-label="Toggle theme"
                title="Toggle theme"
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5 text-yellow-500" />
                ) : (
                  <Moon className="h-5 w-5 text-gray-600" />
                )}
              </button>
            )}
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 flex items-center justify-center p-4 sm:p-6">
          <div className="w-full max-w-md">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 sm:p-8">
              {children}
            </div>
          </div>
        </main>

        {/* Footer */}
        <footer className="w-full p-4 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            © 2024 MedTrack. All rights reserved.
          </p>
        </footer>
      </div>
    </>
  );
} 