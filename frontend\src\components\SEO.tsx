import React from 'react';
import { Metadata } from 'next';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  ogImage?: string;
  ogType?: 'website' | 'article';
  twitterCard?: 'summary' | 'summary_large_image';
  noIndex?: boolean;
}

export function generateMetadata({
  title = 'Medical Learning Platform',
  description = 'Advanced medical education platform for healthcare professionals',
  keywords = ['medical', 'education', 'healthcare', 'learning', 'courses'],
  ogImage = '/images/og-image.jpg',
  ogType = 'website',
  twitterCard = 'summary_large_image',
  noIndex = false
}: SEOProps = {}): Metadata {
  const siteTitle = `${title} | Medical Learning Platform`;

  return {
    title: siteTitle,
    description,
    keywords: keywords.join(', '),
    openGraph: {
      title: siteTitle,
      description,
      type: ogType,
      images: [{ url: ogImage }],
      siteName: 'Medical Learning Platform',
    },
    twitter: {
      card: twitterCard,
      title: siteTitle,
      description,
      images: [ogImage],
    },
    robots: noIndex ? 'noindex,nofollow' : 'index,follow',
    icons: {
      icon: '/favicon.ico',
      apple: '/apple-touch-icon.png',
      shortcut: '/favicon-32x32.png',
    },
    manifest: '/site.webmanifest',
    themeColor: '#4F46E5',
    viewport: 'width=device-width, initial-scale=1',
  };
}

// Example usage in a page.tsx:
// export const metadata = generateMetadata({
//   title: 'Your Page Title',
//   description: 'Your page description'
// });