"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefreshTokenService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const token_blacklist_service_1 = require("./token-blacklist.service");
const uuid_1 = require("uuid");
const cache_service_1 = require("../../cache/cache.service");
let RefreshTokenService = class RefreshTokenService {
    constructor(jwtService, configService, tokenBlacklistService, cacheService) {
        this.jwtService = jwtService;
        this.configService = configService;
        this.tokenBlacklistService = tokenBlacklistService;
        this.cacheService = cacheService;
    }
    async createRefreshToken(userId) {
        const refreshToken = (0, uuid_1.v4)();
        const refreshTokenExpiry = parseInt(this.configService.get('JWT_REFRESH_EXPIRATION', '604800')); // 7 days
        // Store the refresh token with user association
        const tokenData = {
            userId,
            createdAt: new Date().toISOString()
        };
        await this.cacheService.set(`refresh_token:${refreshToken}`, tokenData, refreshTokenExpiry);
        return refreshToken;
    }
    async validateRefreshToken(refreshToken, userId) {
        const tokenData = await this.cacheService.get(`refresh_token:${refreshToken}`);
        if (!tokenData || tokenData.userId !== userId) {
            return false;
        }
        // Check if token is too old (optional additional security)
        const createdAt = new Date(tokenData.createdAt);
        const maxAge = parseInt(this.configService.get('REFRESH_TOKEN_MAX_AGE', '2592000')); // 30 days
        if (Date.now() - createdAt.getTime() > maxAge * 1000) {
            await this.revokeRefreshToken(refreshToken);
            return false;
        }
        return true;
    }
    async revokeRefreshToken(refreshToken) {
        // Remove from cache
        await this.cacheService.delete(`refresh_token:${refreshToken}`);
        // Add to blacklist for extra security
        const blacklistExpiry = parseInt(this.configService.get('JWT_REFRESH_EXPIRATION', '604800')); // 7 days
        await this.tokenBlacklistService.addToBlacklist(refreshToken, blacklistExpiry);
    }
    async revokeAllUserTokens(userId) {
        // This would require scanning all refresh tokens, which might be expensive
        // Consider maintaining a separate index of user->tokens if this is needed frequently
        const pattern = `refresh_token:*`;
        const tokens = await this.cacheService.keys(pattern);
        for (const tokenKey of tokens) {
            const tokenData = await this.cacheService.get(tokenKey);
            if (tokenData && tokenData.userId === userId) {
                const refreshToken = tokenKey.replace('refresh_token:', '');
                await this.revokeRefreshToken(refreshToken);
            }
        }
    }
};
exports.RefreshTokenService = RefreshTokenService;
exports.RefreshTokenService = RefreshTokenService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        config_1.ConfigService,
        token_blacklist_service_1.TokenBlacklistService,
        cache_service_1.CacheService])
], RefreshTokenService);
