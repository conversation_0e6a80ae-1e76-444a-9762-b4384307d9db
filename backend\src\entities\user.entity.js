"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = exports.UserRole = void 0;
const typeorm_1 = require("typeorm");
const progress_entity_1 = require("./progress.entity");
const materials_entity_1 = require("./materials.entity");
const user_response_entity_1 = require("./user-response.entity");
const material_shares_entity_1 = require("./material_shares.entity");
const feedback_entity_1 = require("./feedback.entity");
const notifications_entity_1 = require("./notifications.entity");
const clinical_case_entity_1 = require("./clinical-case.entity");
const study_session_entity_1 = require("./study-session.entity");
const topic_progress_entity_1 = require("./topic-progress.entity");
const flashcard_entity_1 = require("./flashcard.entity");
const role_entity_1 = require("./role.entity");
const cpd_tracking_entity_1 = require("./cpd-tracking.entity");
const cpd_activity_entity_1 = require("./cpd-activity.entity");
const weekly_digest_entity_1 = require("./weekly-digest.entity");
const learning_suggestion_entity_1 = require("./learning-suggestion.entity");
var UserRole;
(function (UserRole) {
    UserRole["STUDENT"] = "student";
    UserRole["TEACHER"] = "teacher";
    UserRole["ADMIN"] = "admin";
})(UserRole || (exports.UserRole = UserRole = {}));
let User = class User {
};
exports.User = User;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], User.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], User.prototype, "username", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], User.prototype, "is_active", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "password_reset_token", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], User.prototype, "password_reset_expires", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "email_verification_token", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], User.prototype, "email_verification_expires", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "email_verified", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], User.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], User.prototype, "password_hash", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], User.prototype, "first_name", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], User.prototype, "last_name", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "profile_picture", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "bio", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "phone_number", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: UserRole, default: UserRole.STUDENT }),
    __metadata("design:type", String)
], User.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "is_locked", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], User.prototype, "locked_until", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], User.prototype, "failed_login_attempts", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => progress_entity_1.Progress, progress => progress.user),
    __metadata("design:type", Array)
], User.prototype, "progress", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => user_response_entity_1.UserResponse, (response) => response.user),
    __metadata("design:type", Array)
], User.prototype, "quiz_responses", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => materials_entity_1.Material, material => material.author),
    __metadata("design:type", Array)
], User.prototype, "created_materials", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => material_shares_entity_1.MaterialShare, (share) => share.shared_by_user),
    __metadata("design:type", Array)
], User.prototype, "shared_materials", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => feedback_entity_1.Feedback, (feedback) => feedback.user),
    __metadata("design:type", Array)
], User.prototype, "feedback", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => notifications_entity_1.Notification, (notification) => notification.user),
    __metadata("design:type", Array)
], User.prototype, "notifications", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => clinical_case_entity_1.ClinicalCase, clinicalCase => clinicalCase.author),
    __metadata("design:type", Array)
], User.prototype, "created_clinical_cases", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], User.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], User.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], User.prototype, "streak_days", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => study_session_entity_1.StudySession, (session) => session.user),
    __metadata("design:type", Array)
], User.prototype, "study_sessions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => topic_progress_entity_1.TopicProgress, (progress) => progress.user),
    __metadata("design:type", Array)
], User.prototype, "topic_progress", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => flashcard_entity_1.Flashcard, flashcard => flashcard.user),
    __metadata("design:type", Array)
], User.prototype, "flashcards", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => role_entity_1.Role),
    (0, typeorm_1.JoinTable)({
        name: 'user_roles',
        joinColumn: { name: 'user_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' }
    }),
    __metadata("design:type", Array)
], User.prototype, "roles", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => cpd_tracking_entity_1.CPDCycle, cycle => cycle.user),
    __metadata("design:type", Array)
], User.prototype, "cpd_cycles", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => cpd_activity_entity_1.CPDActivity, activity => activity.user),
    __metadata("design:type", Array)
], User.prototype, "cpd_activities", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => weekly_digest_entity_1.WeeklyDigest, digest => digest.user),
    __metadata("design:type", Array)
], User.prototype, "weekly_digests", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => learning_suggestion_entity_1.LearningSuggestion, suggestion => suggestion.user),
    __metadata("design:type", Array)
], User.prototype, "learning_suggestions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], User.prototype, "learningHistory", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], User.prototype, "preferences", void 0);
exports.User = User = __decorate([
    (0, typeorm_1.Entity)('users')
], User);
