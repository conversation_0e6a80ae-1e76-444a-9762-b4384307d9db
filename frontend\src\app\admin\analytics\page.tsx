'use client';

import React, { useState } from 'react';
import { BarChart3, Users, BookOpen, Activity, Calendar, TrendingUp } from 'lucide-react';
import styles from './analytics.module.css';

const AnalyticsPage = () => {
  const [timeRange, setTimeRange] = useState('7d');

  // Mock data - in a real app, this would come from an API
  const metrics = [
    {
      title: 'Total Users',
      value: '2,543',
      change: '+12%',
      icon: Users,
      color: 'blue'
    },
    {
      title: 'Active Courses',
      value: '156',
      change: '+5%',
      icon: BookOpen,
      color: 'green'
    },
    {
      title: 'Study Hours',
      value: '12,456',
      change: '+23%',
      icon: Activity,
      color: 'purple'
    },
    {
      title: 'Completion Rate',
      value: '78%',
      change: '+8%',
      icon: TrendingUp,
      color: 'yellow'
    }
  ];

  const roleDistribution = [
    { role: 'Students', count: 1800 },
    { role: 'Instructors', count: 150 },
    { role: 'Residents', count: 300 },
    { role: 'Attending', count: 200 },
    { role: 'Admin', count: 93 }
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
        <select
          aria-label="Select time range"
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value)}
          className="rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="7d">Last 7 days</option>
          <option value="30d">Last 30 days</option>
          <option value="90d">Last 90 days</option>
          <option value="1y">Last year</option>
        </select>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {metrics.map((metric, index) => (
          <div key={index} className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">{metric.title}</p>
                <p className="text-2xl font-bold mt-1">{metric.value}</p>
                <p className={`text-sm ${metric.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                  {metric.change} from last period
                </p>
              </div>
              <div className={`p-3 rounded-lg bg-${metric.color}-100`}>
                <metric.icon className={`w-6 h-6 text-${metric.color}-600`} />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Role Distribution */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 className="text-lg font-semibold mb-4">Role Distribution</h2>
        <div className="space-y-4">
          {roleDistribution.map((item, index) => (
            <div key={index} className="flex items-center">
              <div className="w-32 text-sm text-gray-600">{item.role}</div>
              <div className="flex-1">
                <div
                  className={`${styles.roleBar} ${styles[`roleBarWidth${Math.round((item.count / 2543) * 100)}`]}`}
                />
              </div>
              <div className="w-16 text-right text-sm font-medium">{item.count}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Activity Timeline */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
        <div className="space-y-4">
          {[
            { time: '2 hours ago', event: 'New course "Advanced Cardiology" published' },
            { time: '4 hours ago', event: '50 new students enrolled' },
            { time: '1 day ago', event: 'System maintenance completed' },
            { time: '2 days ago', event: 'New instructor joined the platform' }
          ].map((activity, index) => (
            <div key={index} className="flex items-start space-x-4">
              <div className="w-24 text-sm text-gray-500">{activity.time}</div>
              <div className="flex-1">
                <p className="text-gray-800">{activity.event}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AnalyticsPage; 