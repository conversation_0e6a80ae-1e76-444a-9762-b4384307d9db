import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn, Unique } from 'typeorm';
import { User } from '../../entities/user.entity';
import { Unit } from '../../entities/unit.entity';

@Entity('unit_quiz_scores')
@Unique(['user', 'unit'])
export class UnitQuizScore {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, { eager: true })
  user: User;

  @ManyToOne(() => Unit, { eager: true })
  unit: Unit;

  @Column('int')
  score: number;

  @Column({ default: false })
  passed: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 