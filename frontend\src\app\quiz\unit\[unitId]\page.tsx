"use client";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function UnitQuizPage({ params }) {
  const { unitId } = params;
  const [quiz, setQuiz] = useState(null);
  const [eligible, setEligible] = useState(false);
  const [answers, setAnswers] = useState({});
  const [result, setResult] = useState(null);

  useEffect(() => {
    fetch(`/api/quiz/unit/${unitId}/eligibility`)
      .then(res => res.json())
      .then(data => setEligible(data.eligible));
    fetch(`/api/quiz/unit/${unitId}`)
      .then(res => res.json())
      .then(setQuiz);
  }, [unitId]);

  if (!eligible) return <div>Complete all topics to unlock this quiz.</div>;
  if (!quiz) return <div>Loading...</div>;

  const handleChange = (qid, value) => {
    setAnswers(a => ({ ...a, [qid]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const res = await fetch(`/api/quiz/unit/${unitId}/submit`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ answers }),
    });
    const data = await res.json();
    setResult(data);
  };

  if (result) {
    return (
      <div>
        <h2>Your Score: {result.score}%</h2>
        <p>{result.passed ? "Passed!" : "Try again for mastery."}</p>
        {result.feedback && <div>{result.feedback}</div>}
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit}>
      <h1>{quiz.title}</h1>
      <p>{quiz.instructions}</p>
      {quiz.questions.map(q => (
        <div key={q.id}>
          <p>{q.text}</p>
          {q.options.map(opt => (
            <label key={opt}>
              <input
                type="radio"
                name={q.id}
                value={opt}
                checked={answers[q.id] === opt}
                onChange={() => handleChange(q.id, opt)}
                required
              />
              {opt}
            </label>
          ))}
        </div>
      ))}
      <button type="submit" className="btn-primary">Submit Quiz</button>
    </form>
  );
} 