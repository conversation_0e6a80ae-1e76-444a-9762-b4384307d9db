'use client';

import dynamic from 'next/dynamic';
import { ReactNode } from 'react';
import { Toaster } from 'react-hot-toast';
import { SyncStatusBanner } from '@/components/SyncStatusBanner';
import { ServiceWorkerRegister } from './ServiceWorkerRegister';

// Dynamically import MedTrackLayout with SSR disabled
const MedTrackLayout = dynamic(() => import('./MedTrackLayout'), { ssr: false });

interface ClientLayoutProps {
  children: ReactNode;
}

export function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <MedTrackLayout>
      <ServiceWorkerRegister />
      <Toaster position="top-right" />
      <SyncStatusBanner />
      {children}
    </MedTrackLayout>
  );
}
