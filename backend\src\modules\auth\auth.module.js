"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
// src/modules/auth/auth.module.ts
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const auth_controller_1 = require("./auth.controller");
const auth_service_1 = require("./auth.service");
const jwt_strategy_1 = require("./jwt.strategy");
const security_service_1 = require("./security.service");
const refresh_token_service_1 = require("./refresh-token.service");
const token_blacklist_service_1 = require("./token-blacklist.service");
const users_module_1 = require("../users/users.module");
// Import entities
const user_entity_1 = require("../../entities/user.entity");
const security_entity_1 = require("../../entities/security.entity");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            // Import TypeORM features for ALL entities used in this module
            typeorm_1.TypeOrmModule.forFeature([
                user_entity_1.User,
                security_entity_1.UserSession,
                security_entity_1.SecurityEvent,
                security_entity_1.UserSecuritySettings, // Add the missing entity
            ]),
            // Import PassportModule
            passport_1.PassportModule.register({ defaultStrategy: 'jwt' }),
            // Import UsersModule to provide UsersService
            users_module_1.UsersModule,
            // Configure JWT Module
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    secret: configService.get('JWT_SECRET'),
                    signOptions: {
                        expiresIn: configService.get('JWT_EXPIRES_IN', '1d'),
                    },
                }),
                inject: [config_1.ConfigService],
            }),
        ],
        controllers: [auth_controller_1.AuthController],
        providers: [
            auth_service_1.AuthService,
            jwt_strategy_1.JwtStrategy,
            security_service_1.SecurityService,
            refresh_token_service_1.RefreshTokenService,
            token_blacklist_service_1.TokenBlacklistService,
        ],
        exports: [auth_service_1.AuthService, jwt_1.JwtModule, passport_1.PassportModule],
    })
], AuthModule);
