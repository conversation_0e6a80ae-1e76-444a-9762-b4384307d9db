"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerifyRecoveryDto = exports.AccountRecoveryDto = exports.RevokeSessionDto = exports.ResendVerificationDto = exports.EmailVerificationDto = exports.VerifyTwoFactorDto = exports.TwoFactorSetupDto = exports.ChangePasswordDto = exports.SecuritySettingsDto = void 0;
// src/dto/auth/security.dto.ts
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class SecuritySettingsDto {
}
exports.SecuritySettingsDto = SecuritySettingsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Enable two-factor authentication', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SecuritySettingsDto.prototype, "twoFactorEnabled", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Login notification settings', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SecuritySettingsDto.prototype, "loginNotifications", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Account activity notifications', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SecuritySettingsDto.prototype, "activityNotifications", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Session timeout in minutes', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], SecuritySettingsDto.prototype, "sessionTimeout", void 0);
class ChangePasswordDto {
}
exports.ChangePasswordDto = ChangePasswordDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Current password' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1),
    __metadata("design:type", String)
], ChangePasswordDto.prototype, "currentPassword", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'New password' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(8),
    __metadata("design:type", String)
], ChangePasswordDto.prototype, "newPassword", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Confirm new password' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(8),
    __metadata("design:type", String)
], ChangePasswordDto.prototype, "confirmPassword", void 0);
class TwoFactorSetupDto {
}
exports.TwoFactorSetupDto = TwoFactorSetupDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User ID for 2FA setup' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TwoFactorSetupDto.prototype, "userId", void 0);
class VerifyTwoFactorDto {
}
exports.VerifyTwoFactorDto = VerifyTwoFactorDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'TOTP token from authenticator app' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(6),
    __metadata("design:type", String)
], VerifyTwoFactorDto.prototype, "token", void 0);
class EmailVerificationDto {
}
exports.EmailVerificationDto = EmailVerificationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Email verification token' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EmailVerificationDto.prototype, "token", void 0);
class ResendVerificationDto {
}
exports.ResendVerificationDto = ResendVerificationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Email address to resend verification' }),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], ResendVerificationDto.prototype, "email", void 0);
class RevokeSessionDto {
}
exports.RevokeSessionDto = RevokeSessionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Session ID to revoke' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RevokeSessionDto.prototype, "sessionId", void 0);
class AccountRecoveryDto {
}
exports.AccountRecoveryDto = AccountRecoveryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Email address for account recovery' }),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], AccountRecoveryDto.prototype, "email", void 0);
class VerifyRecoveryDto {
}
exports.VerifyRecoveryDto = VerifyRecoveryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Recovery token' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], VerifyRecoveryDto.prototype, "token", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Security question answers' }),
    __metadata("design:type", Object)
], VerifyRecoveryDto.prototype, "answers", void 0);
