(()=>{var e={"./dist/compiled/@edge-runtime/cookies/index.js":e=>{"use strict";var r=Object.defineProperty,t=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,i={};function a(e){var r;let t=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(r=e.value)?r:"")}`;return 0===t.length?n:`${n}; ${t.join("; ")}`}function s(e){let r=/* @__PURE__ */new Map;for(let t of e.split(/; */)){if(!t)continue;let e=t.indexOf("=");if(-1===e){r.set(t,"true");continue}let[n,o]=[t.slice(0,e),t.slice(e+1)];try{r.set(n,decodeURIComponent(null!=o?o:"true"))}catch{}}return r}function d(e){var r,t;if(!e)return;let[[n,o],...i]=s(e),{domain:a,expires:d,httponly:p,maxage:c,path:f,samesite:g,secure:v,partitioned:m,priority:h}=Object.fromEntries(i.map(([e,r])=>[e.toLowerCase().replace(/-/g,""),r]));return function(e){let r={};for(let t in e)e[t]&&(r[t]=e[t]);return r}({name:n,value:decodeURIComponent(o),domain:a,...d&&{expires:new Date(d)},...p&&{httpOnly:!0},..."string"==typeof c&&{maxAge:Number(c)},path:f,...g&&{sameSite:u.includes(r=(r=g).toLowerCase())?r:void 0},...v&&{secure:!0},...h&&{priority:l.includes(t=(t=h).toLowerCase())?t:void 0},...m&&{partitioned:!0}})}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(i,{RequestCookies:()=>p,ResponseCookies:()=>c,parseCookie:()=>s,parseSetCookie:()=>d,stringifyCookie:()=>a}),e.exports=((e,i,a,s)=>{if(i&&"object"==typeof i||"function"==typeof i)for(let a of n(i))o.call(e,a)||void 0===a||r(e,a,{get:()=>i[a],enumerable:!(s=t(i,a))||s.enumerable});return e})(r({},"__esModule",{value:!0}),i);var u=["strict","lax","none"],l=["low","medium","high"],p=class{constructor(e){this._parsed=/* @__PURE__ */new Map,this._headers=e;let r=e.get("cookie");if(r)for(let[e,t]of s(r))this._parsed.set(e,{name:e,value:t})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let r="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(r)}getAll(...e){var r;let t=Array.from(this._parsed);if(!e.length)return t.map(([e,r])=>r);let n="string"==typeof e[0]?e[0]:null==(r=e[0])?void 0:r.name;return t.filter(([e])=>e===n).map(([e,r])=>r)}has(e){return this._parsed.has(e)}set(...e){let[r,t]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(r,{name:r,value:t}),this._headers.set("cookie",Array.from(n).map(([e,r])=>a(r)).join("; ")),this}delete(e){let r=this._parsed,t=Array.isArray(e)?e.map(e=>r.delete(e)):r.delete(e);return this._headers.set("cookie",Array.from(r).map(([e,r])=>a(r)).join("; ")),t}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},c=class{constructor(e){var r,t,n;this._parsed=/* @__PURE__ */new Map,this._headers=e;let o=null!=(n=null!=(t=null==(r=e.getSetCookie)?void 0:r.call(e))?t:e.get("set-cookie"))?n:[];for(let e of Array.isArray(o)?o:function(e){if(!e)return[];var r,t,n,o,i,a=[],s=0;function d(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(r=s,i=!1;d();)if(","===(t=e.charAt(s))){for(n=s,s+=1,d(),o=s;s<e.length&&"="!==(t=e.charAt(s))&&";"!==t&&","!==t;)s+=1;s<e.length&&"="===e.charAt(s)?(i=!0,s=o,a.push(e.substring(r,n)),r=s):s=n+1}else s+=1;(!i||s>=e.length)&&a.push(e.substring(r,e.length))}return a}(o)){let r=d(e);r&&this._parsed.set(r.name,r)}}get(...e){let r="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(r)}getAll(...e){var r;let t=Array.from(this._parsed.values());if(!e.length)return t;let n="string"==typeof e[0]?e[0]:null==(r=e[0])?void 0:r.name;return t.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[r,t,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,o=this._parsed;return o.set(r,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:r,value:t,...n})),function(e,r){for(let[,t]of(r.delete("set-cookie"),e)){let e=a(t);r.append("set-cookie",e)}}(o,this._headers),this}delete(...e){let[r,t]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...t,name:r,value:"",expires:/* @__PURE__ */new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},"./dist/compiled/bytes/index.js":e=>{(()=>{"use strict";var r={56:e=>{/*!
 * bytes
 * Copyright(c) 2012-2014 TJ Holowaychuk
 * Copyright(c) 2015 Jed Watson
 * MIT Licensed
 */e.exports=function(e,r){return"string"==typeof e?a(e):"number"==typeof e?i(e,r):null},e.exports.format=i,e.exports.parse=a;var r=/\B(?=(\d{3})+(?!\d))/g,t=/(?:\.0*|(\.[^0]+)0+)$/,n={b:1,kb:1024,mb:1048576,gb:0x40000000,tb:0x10000000000,pb:0x4000000000000},o=/^((-|\+)?(\d+(?:\.\d+)?)) *(kb|mb|gb|tb|pb)$/i;function i(e,o){if(!Number.isFinite(e))return null;var i=Math.abs(e),a=o&&o.thousandsSeparator||"",s=o&&o.unitSeparator||"",d=o&&void 0!==o.decimalPlaces?o.decimalPlaces:2,u=!!(o&&o.fixedDecimals),l=o&&o.unit||"";l&&n[l.toLowerCase()]||(l=i>=n.pb?"PB":i>=n.tb?"TB":i>=n.gb?"GB":i>=n.mb?"MB":i>=n.kb?"KB":"B");var p=(e/n[l.toLowerCase()]).toFixed(d);return u||(p=p.replace(t,"$1")),a&&(p=p.split(".").map(function(e,t){return 0===t?e.replace(r,a):e}).join(".")),p+s+l}function a(e){if("number"==typeof e&&!isNaN(e))return e;if("string"!=typeof e)return null;var r,t=o.exec(e),i="b";return t?(r=parseFloat(t[1]),i=t[4].toLowerCase()):(r=parseInt(e,10),i="b"),Math.floor(n[i]*r)}}},t={};function n(e){var o=t[e];if(void 0!==o)return o.exports;var i=t[e]={exports:{}},a=!0;try{r[e](i,i.exports,n),a=!1}finally{a&&delete t[e]}return i.exports}n.ab=__dirname+"/";var o=n(56);e.exports=o})()},"./dist/compiled/content-type/index.js":e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var r={};(()=>{/*!
 * content-type
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var e=/; *([!#$%&'*+.^_`|~0-9A-Za-z-]+) *= *("(?:[\u000b\u0020\u0021\u0023-\u005b\u005d-\u007e\u0080-\u00ff]|\\[\u000b\u0020-\u00ff])*"|[!#$%&'*+.^_`|~0-9A-Za-z-]+) */g,t=/^[\u000b\u0020-\u007e\u0080-\u00ff]+$/,n=/^[!#$%&'*+.^_`|~0-9A-Za-z-]+$/,o=/\\([\u000b\u0020-\u00ff])/g,i=/([\\"])/g,a=/^[!#$%&'*+.^_`|~0-9A-Za-z-]+\/[!#$%&'*+.^_`|~0-9A-Za-z-]+$/;function s(e){this.parameters=Object.create(null),this.type=e}r.format=function(e){if(!e||"object"!=typeof e)throw TypeError("argument obj is required");var r=e.parameters,o=e.type;if(!o||!a.test(o))throw TypeError("invalid type");var s=o;if(r&&"object"==typeof r)for(var d,u=Object.keys(r).sort(),l=0;l<u.length;l++){if(d=u[l],!n.test(d))throw TypeError("invalid parameter name");s+="; "+d+"="+function(e){var r=String(e);if(n.test(r))return r;if(r.length>0&&!t.test(r))throw TypeError("invalid parameter value");return'"'+r.replace(i,"\\$1")+'"'}(r[d])}return s},r.parse=function(r){if(!r)throw TypeError("argument string is required");var t,n,i,d="object"==typeof r?function(e){var r;if("function"==typeof e.getHeader?r=e.getHeader("content-type"):"object"==typeof e.headers&&(r=e.headers&&e.headers["content-type"]),"string"!=typeof r)throw TypeError("content-type header is missing from object");return r}(r):r;if("string"!=typeof d)throw TypeError("argument string is required to be a string");var u=d.indexOf(";"),l=-1!==u?d.substr(0,u).trim():d.trim();if(!a.test(l))throw TypeError("invalid media type");var p=new s(l.toLowerCase());if(-1!==u){for(e.lastIndex=u;n=e.exec(d);){if(n.index!==u)throw TypeError("invalid parameter format");u+=n[0].length,t=n[1].toLowerCase(),'"'===(i=n[2])[0]&&(i=i.substr(1,i.length-2).replace(o,"$1")),p.parameters[t]=i}if(u!==d.length)throw TypeError("invalid parameter format")}return p}})(),e.exports=r})()},"./dist/compiled/cookie/index.js":e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var r={};(()=>{/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */r.parse=function(r,t){if("string"!=typeof r)throw TypeError("argument str must be a string");for(var o={},i=r.split(n),a=(t||{}).decode||e,s=0;s<i.length;s++){var d=i[s],u=d.indexOf("=");if(!(u<0)){var l=d.substr(0,u).trim(),p=d.substr(++u,d.length).trim();'"'==p[0]&&(p=p.slice(1,-1)),void 0==o[l]&&(o[l]=function(e,r){try{return r(e)}catch(r){return e}}(p,a))}}return o},r.serialize=function(e,r,n){var i=n||{},a=i.encode||t;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var s=a(r);if(s&&!o.test(s))throw TypeError("argument val is invalid");var d=e+"="+s;if(null!=i.maxAge){var u=i.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");d+="; Max-Age="+Math.floor(u)}if(i.domain){if(!o.test(i.domain))throw TypeError("option domain is invalid");d+="; Domain="+i.domain}if(i.path){if(!o.test(i.path))throw TypeError("option path is invalid");d+="; Path="+i.path}if(i.expires){if("function"!=typeof i.expires.toUTCString)throw TypeError("option expires is invalid");d+="; Expires="+i.expires.toUTCString()}if(i.httpOnly&&(d+="; HttpOnly"),i.secure&&(d+="; Secure"),i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":d+="; SameSite=Strict";break;case"lax":d+="; SameSite=Lax";break;case"none":d+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return d};var e=decodeURIComponent,t=encodeURIComponent,n=/; */,o=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=r})()},"./dist/compiled/fresh/index.js":e=>{(()=>{"use strict";var r={695:e=>{/*!
 * fresh
 * Copyright(c) 2012 TJ Holowaychuk
 * Copyright(c) 2016-2017 Douglas Christopher Wilson
 * MIT Licensed
 */var r=/(?:^|,)\s*?no-cache\s*?(?:,|$)/;function t(e){var r=e&&Date.parse(e);return"number"==typeof r?r:NaN}e.exports=function(e,n){var o=e["if-modified-since"],i=e["if-none-match"];if(!o&&!i)return!1;var a=e["cache-control"];if(a&&r.test(a))return!1;if(i&&"*"!==i){var s=n.etag;if(!s)return!1;for(var d=!0,u=function(e){for(var r=0,t=[],n=0,o=0,i=e.length;o<i;o++)switch(e.charCodeAt(o)){case 32:n===r&&(n=r=o+1);break;case 44:t.push(e.substring(n,r)),n=r=o+1;break;default:r=o+1}return t.push(e.substring(n,r)),t}(i),l=0;l<u.length;l++){var p=u[l];if(p===s||p==="W/"+s||"W/"+p===s){d=!1;break}}if(d)return!1}if(o){var c=n["last-modified"];if(!c||!(t(c)<=t(o)))return!1}return!0}}},t={};function n(e){var o=t[e];if(void 0!==o)return o.exports;var i=t[e]={exports:{}},a=!0;try{r[e](i,i.exports,n),a=!1}finally{a&&delete t[e]}return i.exports}n.ab=__dirname+"/";var o=n(695);e.exports=o})()},"./dist/esm/server/crypto-utils.js":(e,r,t)=>{"use strict";t.r(r),t.d(r,{decryptWithSecret:()=>s,encryptWithSecret:()=>a});let n=require("crypto");var o=/*#__PURE__*/t.n(n);let i="aes-256-gcm";function a(e,r){let t=o().randomBytes(16),n=o().randomBytes(64),a=o().pbkdf2Sync(e,n,1e5,32,"sha512"),s=o().createCipheriv(i,a,t),d=Buffer.concat([s.update(r,"utf8"),s.final()]),u=s.getAuthTag();return Buffer.concat([n,t,u,d]).toString("hex")}function s(e,r){let t=Buffer.from(r,"hex"),n=t.slice(0,64),a=t.slice(64,80),s=t.slice(80,96),d=t.slice(96),u=o().pbkdf2Sync(e,n,1e5,32,"sha512"),l=o().createDecipheriv(i,u,a);return l.setAuthTag(s),l.update(d)+l.final("utf8")}},"next/dist/compiled/jsonwebtoken":e=>{"use strict";e.exports=require("next/dist/compiled/jsonwebtoken")},"next/dist/compiled/raw-body":e=>{"use strict";e.exports=require("next/dist/compiled/raw-body")},querystring:e=>{"use strict";e.exports=require("querystring")}},r={};function t(n){var o=r[n];if(void 0!==o)return o.exports;var i=r[n]={exports:{}};return e[n](i,i.exports,t),i.exports}t.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return t.d(r,{a:r}),r},t.d=(e,r)=>{for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},t.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";t.r(n),t.d(n,{PagesAPIRouteModule:()=>z,default:()=>U});class e{static get(e,r,t){let n=Reflect.get(e,r,t);return"function"==typeof n?n.bind(e):n}static set(e,r,t,n){return Reflect.set(e,r,t,n)}static has(e,r){return Reflect.has(e,r)}static deleteProperty(e,r){return Reflect.deleteProperty(e,r)}}class r extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new r}}class o extends Headers{constructor(r){super(),this.headers=new Proxy(r,{get(t,n,o){if("symbol"==typeof n)return e.get(t,n,o);let i=n.toLowerCase(),a=Object.keys(r).find(e=>e.toLowerCase()===i);if(void 0!==a)return e.get(t,a,o)},set(t,n,o,i){if("symbol"==typeof n)return e.set(t,n,o,i);let a=n.toLowerCase(),s=Object.keys(r).find(e=>e.toLowerCase()===a);return e.set(t,s??n,o,i)},has(t,n){if("symbol"==typeof n)return e.has(t,n);let o=n.toLowerCase(),i=Object.keys(r).find(e=>e.toLowerCase()===o);return void 0!==i&&e.has(t,i)},deleteProperty(t,n){if("symbol"==typeof n)return e.deleteProperty(t,n);let o=n.toLowerCase(),i=Object.keys(r).find(e=>e.toLowerCase()===o);return void 0===i||e.deleteProperty(t,i)}})}static seal(t){return new Proxy(t,{get(t,n,o){switch(n){case"append":case"delete":case"set":return r.callable;default:return e.get(t,n,o)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new o(e)}append(e,r){let t=this.headers[e];"string"==typeof t?this.headers[e]=[t,r]:Array.isArray(t)?t.push(r):this.headers[e]=r}delete(e){delete this.headers[e]}get(e){let r=this.headers[e];return void 0!==r?this.merge(r):null}has(e){return void 0!==this.headers[e]}set(e,r){this.headers[e]=r}forEach(e,r){for(let[t,n]of this.entries())e.call(r,n,t,this)}*entries(){for(let e of Object.keys(this.headers)){let r=e.toLowerCase(),t=this.get(r);yield[r,t]}}*keys(){for(let e of Object.keys(this.headers)){let r=e.toLowerCase();yield r}}*values(){for(let e of Object.keys(this.headers)){let r=this.get(e);yield r}}[Symbol.iterator](){return this.entries()}}let i="x-prerender-revalidate",a="x-prerender-revalidate-if-generated",s={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};({...s,GROUP:{builtinReact:[s.reactServerComponents,s.actionBrowser],serverOnly:[s.reactServerComponents,s.actionBrowser,s.instrument,s.middleware],neutralTarget:[s.apiNode,s.apiEdge],clientOnly:[s.serverSideRendering,s.appPagesBrowser],bundled:[s.reactServerComponents,s.actionBrowser,s.serverSideRendering,s.appPagesBrowser,s.shared,s.instrument,s.middleware],appPages:[s.reactServerComponents,s.serverSideRendering,s.appPagesBrowser,s.actionBrowser]}});let d=require("next/dist/server/lib/trace/tracer");var u=/*#__PURE__*/function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(u||{}),l=/*#__PURE__*/function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(l||{}),p=/*#__PURE__*/function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(p||{}),c=/*#__PURE__*/function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(c||{}),f=/*#__PURE__*/function(e){return e.startServer="startServer.startServer",e}(f||{}),g=/*#__PURE__*/function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(g||{}),v=/*#__PURE__*/function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(v||{}),m=/*#__PURE__*/function(e){return e.executeRoute="Router.executeRoute",e}(m||{}),h=/*#__PURE__*/function(e){return e.runHandler="Node.runHandler",e}(h||{}),y=/*#__PURE__*/function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(y||{}),b=/*#__PURE__*/function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(b||{}),x=/*#__PURE__*/function(e){return e.execute="Middleware.execute",e}(x||{});let w="__prerender_bypass",S="__next_preview_data",R=Symbol(S),_=Symbol(w);function E(e,r={}){if(_ in e)return e;let{serialize:n}=t("./dist/compiled/cookie/index.js"),o=e.getHeader("Set-Cookie");return e.setHeader("Set-Cookie",[..."string"==typeof o?[o]:Array.isArray(o)?o:[],n(w,"",{expires:new Date(0),httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==r.path?{path:r.path}:void 0}),n(S,"",{expires:new Date(0),httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==r.path?{path:r.path}:void 0})]),Object.defineProperty(e,_,{value:!0,enumerable:!1}),e}class O extends Error{constructor(e,r){super(r),this.statusCode=e}}function C(e,r,t){e.statusCode=r,e.statusMessage=t,e.end(t)}function N({req:e},r,t){let n={configurable:!0,enumerable:!0},o={...n,writable:!0};Object.defineProperty(e,r,{...n,get:()=>{let n=t();return Object.defineProperty(e,r,{...o,value:n}),n},set:t=>{Object.defineProperty(e,r,{...o,value:t})}})}class j{constructor({userland:e,definition:r}){this.userland=e,this.definition=r}}var T=t("./dist/compiled/bytes/index.js"),P=/*#__PURE__*/t.n(T);let A=e=>{let r=e.length,t=0,n=0,o=8997,i=0,a=33826,s=0,d=40164,u=0,l=52210;for(;t<r;)o^=e.charCodeAt(t++),n=435*o,i=435*a,s=435*d,u=435*l,s+=o<<8,u+=a<<8,i+=n>>>16,o=65535&n,s+=i>>>16,a=65535&i,l=u+(s>>>16)&65535,d=65535&s;return(15&l)*0x1000000000000+0x100000000*d+65536*a+(o^l>>4)},H=(e,r=!1)=>(r?'W/"':'"')+A(e).toString(36)+e.length.toString(36)+'"';"undefined"!=typeof performance&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);var M=t("./dist/compiled/fresh/index.js"),k=/*#__PURE__*/t.n(M);let B=require("stream");function L(e){return"object"==typeof e&&null!==e&&"name"in e&&"message"in e}var D=t("./dist/compiled/@edge-runtime/cookies/index.js"),$=t("./dist/compiled/content-type/index.js");async function q(e,r){let n,o;try{n=(0,$.parse)(e.headers["content-type"]||"text/plain")}catch{n=(0,$.parse)("text/plain")}let{type:i,parameters:a}=n,s=a.charset||"utf-8";try{let n=t("next/dist/compiled/raw-body");o=await n(e,{encoding:s,limit:r})}catch(e){if(L(e)&&"entity.too.large"===e.type)throw Object.defineProperty(new O(413,`Body exceeded ${r} limit`),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw Object.defineProperty(new O(400,"Invalid body"),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}let d=o.toString();return"application/json"===i||"application/ld+json"===i?function(e){if(0===e.length)return{};try{return JSON.parse(e)}catch(e){throw Object.defineProperty(new O(400,"Invalid JSON"),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}(d):"application/x-www-form-urlencoded"===i?t("querystring").decode(d):d}function I(e){return"string"==typeof e&&e.length>=16}async function K(e,r,t,n){if("string"!=typeof e||!e.startsWith("/"))throw Object.defineProperty(Error(`Invalid urlPath provided to revalidate(), must be a path e.g. /blog/post-1, received ${e}`),"__NEXT_ERROR_CODE",{value:"E153",enumerable:!1,configurable:!0});let o={[i]:n.previewModeId,...r.unstable_onlyGenerated?{[a]:"1"}:{}},s=[...n.allowedRevalidateHeaderKeys||[]];for(let e of((n.trustHostHeader||n.dev)&&s.push("cookie"),n.trustHostHeader&&s.push("x-vercel-protection-bypass"),Object.keys(t.headers)))s.includes(e)&&(o[e]=t.headers[e]);try{if(n.trustHostHeader){let n=await fetch(`https://${t.headers.host}${e}`,{method:"HEAD",headers:o}),i=n.headers.get("x-vercel-cache")||n.headers.get("x-nextjs-cache");if((null==i?void 0:i.toUpperCase())!=="REVALIDATED"&&200!==n.status&&!(404===n.status&&r.unstable_onlyGenerated))throw Object.defineProperty(Error(`Invalid response ${n.status}`),"__NEXT_ERROR_CODE",{value:"E175",enumerable:!1,configurable:!0})}else if(n.revalidate)await n.revalidate({urlPath:e,revalidateHeaders:o,opts:r});else throw Object.defineProperty(Error("Invariant: required internal revalidate method not passed to api-utils"),"__NEXT_ERROR_CODE",{value:"E174",enumerable:!1,configurable:!0})}catch(r){throw Object.defineProperty(Error(`Failed to revalidate ${e}: ${L(r)?r.message:r}`),"__NEXT_ERROR_CODE",{value:"E240",enumerable:!1,configurable:!0})}}async function X(e,r,n,s,d,u,l,p,c){try{var f,g,v,m;if(!s){r.statusCode=404,r.end("Not Found");return}let u=s.config||{},l=(null==(f=u.api)?void 0:f.bodyParser)!==!1,p=(null==(g=u.api)?void 0:g.responseLimit)??!0;null==(v=u.api)||v.externalResolver,N({req:e},"cookies",(m=e.headers,function(){let{cookie:e}=m;if(!e)return{};let{parse:r}=t("./dist/compiled/cookie/index.js");return r(Array.isArray(e)?e.join("; "):e)})),e.query=n,N({req:e},"previewData",()=>(function(e,r,n,s){var d,u;let l;if(n&&function(e,r){let t=o.from(e.headers);return{isOnDemandRevalidate:t.get(i)===r.previewModeId,revalidateOnlyGenerated:t.has(a)}}(e,n).isOnDemandRevalidate)return!1;if(R in e)return e[R];let p=o.from(e.headers),c=new D.RequestCookies(p),f=null==(d=c.get(w))?void 0:d.value,g=null==(u=c.get(S))?void 0:u.value;if(f&&!g&&f===n.previewModeId){let r={};return Object.defineProperty(e,R,{value:r,enumerable:!1}),r}if(!f&&!g)return!1;if(!f||!g||f!==n.previewModeId)return s||E(r),!1;try{l=t("next/dist/compiled/jsonwebtoken").verify(g,n.previewModeSigningKey)}catch{return E(r),!1}let{decryptWithSecret:v}=t("./dist/esm/server/crypto-utils.js"),m=v(Buffer.from(n.previewModeEncryptionKey),l.data);try{let r=JSON.parse(m);return Object.defineProperty(e,R,{value:r,enumerable:!1}),r}catch{return!1}})(e,r,d,!!d.multiZoneDraftMode)),N({req:e},"preview",()=>!1!==e.previewData||void 0),N({req:e},"draftMode",()=>e.preview),l&&!e.body&&(e.body=await q(e,u.api&&u.api.bodyParser&&u.api.bodyParser.sizeLimit?u.api.bodyParser.sizeLimit:"1mb"));let c=0,h=p&&"boolean"!=typeof p?P().parse(p):4194304,y=r.write,b=r.end;r.write=(...e)=>(c+=Buffer.byteLength(e[0]||""),y.apply(r,e)),r.end=(...t)=>(t.length&&"function"!=typeof t[0]&&(c+=Buffer.byteLength(t[0]||"")),p&&c>=h&&console.warn(`API response for ${e.url} exceeds ${P().format(h)}. API Routes are meant to respond quickly. https://nextjs.org/docs/messages/api-routes-response-size-limit`),b.apply(r,t)),r.status=e=>(r.statusCode=e,r),r.send=t=>(function(e,r,t){var n;if(null==t){r.end();return}if(204===r.statusCode||304===r.statusCode){r.removeHeader("Content-Type"),r.removeHeader("Content-Length"),r.removeHeader("Transfer-Encoding"),r.end();return}let o=r.getHeader("Content-Type");if(t instanceof B.Stream){o||r.setHeader("Content-Type","application/octet-stream"),t.pipe(r);return}let i=["object","number","boolean"].includes(typeof t),a=i?JSON.stringify(t):t;if((n=H(a))&&r.setHeader("ETag",n),!k()(e.headers,{etag:n})||(r.statusCode=304,r.end(),0)){if(Buffer.isBuffer(t)){o||r.setHeader("Content-Type","application/octet-stream"),r.setHeader("Content-Length",t.length),r.end(t);return}i&&r.setHeader("Content-Type","application/json; charset=utf-8"),r.setHeader("Content-Length",Buffer.byteLength(a)),r.end(a)}})(e,r,t),r.json=e=>{r.setHeader("Content-Type","application/json; charset=utf-8"),r.send(JSON.stringify(e))},r.redirect=(e,t)=>(function(e,r,t){if("string"==typeof r&&(t=r,r=307),"number"!=typeof r||"string"!=typeof t)throw Object.defineProperty(Error("Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination')."),"__NEXT_ERROR_CODE",{value:"E389",enumerable:!1,configurable:!0});return e.writeHead(r,{Location:t}),e.write(t),e.end(),e})(r,e,t),r.setDraftMode=(e={enable:!0})=>(function(e,r){if(!I(r.previewModeId))throw Object.defineProperty(Error("invariant: invalid previewModeId"),"__NEXT_ERROR_CODE",{value:"E169",enumerable:!1,configurable:!0});let n=r.enable?void 0:new Date(0),{serialize:o}=t("./dist/compiled/cookie/index.js"),i=e.getHeader("Set-Cookie");return e.setHeader("Set-Cookie",[..."string"==typeof i?[i]:Array.isArray(i)?i:[],o(w,r.previewModeId,{httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:n})]),e})(r,Object.assign({},d,e)),r.setPreviewData=(e,n={})=>(function(e,r,n){if(!I(n.previewModeId))throw Object.defineProperty(Error("invariant: invalid previewModeId"),"__NEXT_ERROR_CODE",{value:"E169",enumerable:!1,configurable:!0});if(!I(n.previewModeEncryptionKey))throw Object.defineProperty(Error("invariant: invalid previewModeEncryptionKey"),"__NEXT_ERROR_CODE",{value:"E334",enumerable:!1,configurable:!0});if(!I(n.previewModeSigningKey))throw Object.defineProperty(Error("invariant: invalid previewModeSigningKey"),"__NEXT_ERROR_CODE",{value:"E436",enumerable:!1,configurable:!0});let o=t("next/dist/compiled/jsonwebtoken"),{encryptWithSecret:i}=t("./dist/esm/server/crypto-utils.js"),a=o.sign({data:i(Buffer.from(n.previewModeEncryptionKey),JSON.stringify(r))},n.previewModeSigningKey,{algorithm:"HS256",...void 0!==n.maxAge?{expiresIn:n.maxAge}:void 0});if(a.length>2048)throw Object.defineProperty(Error("Preview data is limited to 2KB currently, reduce how much data you are storing as preview data to continue"),"__NEXT_ERROR_CODE",{value:"E465",enumerable:!1,configurable:!0});let{serialize:s}=t("./dist/compiled/cookie/index.js"),d=e.getHeader("Set-Cookie");return e.setHeader("Set-Cookie",[..."string"==typeof d?[d]:Array.isArray(d)?d:[],s(w,n.previewModeId,{httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==n.maxAge?{maxAge:n.maxAge}:void 0,...void 0!==n.path?{path:n.path}:void 0}),s(S,a,{httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==n.maxAge?{maxAge:n.maxAge}:void 0,...void 0!==n.path?{path:n.path}:void 0})]),e})(r,e,Object.assign({},d,n)),r.clearPreviewData=(e={})=>E(r,e),r.revalidate=(r,t)=>K(r,t||{},e,d);let x=s.default||s;await x(e,r)}catch(t){if(null==c||c(t,e,{routerKind:"Pages Router",routePath:p||"",routeType:"route",revalidateReason:void 0}),t instanceof O)C(r,t.statusCode,t.message);else{if(l)throw L(t)&&(t.page=p),t;if(console.error(t),u)throw t;C(r,500,"Internal Server Error")}}}class z extends j{constructor(e){if(super(e),"function"!=typeof e.userland.default)throw Object.defineProperty(Error(`Page ${e.definition.page} does not export a default function.`),"__NEXT_ERROR_CODE",{value:"E379",enumerable:!1,configurable:!0});this.apiResolverWrapped=function(e,r){return(...t)=>((0,d.getTracer)().setRootSpanAttribute("next.route",e),(0,d.getTracer)().trace(h.runHandler,{spanName:`executing api route (pages) ${e}`},()=>r(...t)))}(e.definition.page,X)}async render(e,r,t){let{apiResolverWrapped:n}=this;await n(e,r,t.query,this.userland,{...t.previewProps,revalidate:t.revalidate,trustHostHeader:t.trustHostHeader,allowedRevalidateHeaderKeys:t.allowedRevalidateHeaderKeys,hostname:t.hostname,multiZoneDraftMode:t.multiZoneDraftMode,dev:t.dev},t.minimalMode,t.dev,t.page,t.onError)}}let U=z})(),module.exports=n})();
//# sourceMappingURL=pages-api.runtime.prod.js.map