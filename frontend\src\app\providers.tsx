'use client';

import React, { createContext, useContext, useLayoutEffect, useState, ReactNode } from 'react';
import { AuthProvider } from '../contexts/AuthContext';

// Theme context interface
interface ThemeContextType {
  theme: string;
  setTheme: (theme: string) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Custom hook to access theme context
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Custom theme provider to manage light/dark mode
const SimpleThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [theme, setTheme] = useState<string>('light');
  const [mounted, setMounted] = useState(false);

  // Only access localStorage and modify DOM after component mounts on client
  useLayoutEffect(() => {
    const savedTheme = localStorage.getItem('medtrack-theme') || 'light';
    setTheme(savedTheme);
    document.documentElement.classList.toggle('dark', savedTheme === 'dark');
    setMounted(true);
  }, []);

  const updateTheme = (newTheme: string): void => {
    setTheme(newTheme);
    localStorage.setItem('medtrack-theme', newTheme);
    document.documentElement.classList.toggle('dark', newTheme === 'dark');
  };

  // Only render children after initial theme is set on client
  if (!mounted) {
    return null;
  }

  return (
    <ThemeContext.Provider value={{ theme, setTheme: updateTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Providers wrapper for theme and auth
export function Providers({ children }: { children: ReactNode }): JSX.Element {
  return (
    <SimpleThemeProvider>
      <AuthProvider>{children}</AuthProvider>
    </SimpleThemeProvider>
  );
}