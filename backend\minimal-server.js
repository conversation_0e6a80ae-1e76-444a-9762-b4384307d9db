"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MinimalAppModule = void 0;
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const auth_module_1 = require("./src/modules/auth/auth.module");
const users_module_1 = require("./src/modules/users/users.module");
const cache_module_1 = require("./src/cache/cache.module");
const user_entity_1 = require("./src/entities/user.entity");
const common_2 = require("@nestjs/common");
let MinimalAppModule = class MinimalAppModule {
};
exports.MinimalAppModule = MinimalAppModule;
exports.MinimalAppModule = MinimalAppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
            typeorm_1.TypeOrmModule.forRoot({
                type: 'postgres',
                host: process.env.POSTGRES_HOST || 'localhost',
                port: parseInt(process.env.POSTGRES_PORT) || 5432,
                username: process.env.POSTGRES_USER || 'medical',
                password: process.env.POSTGRES_PASSWORD || 'AU110s/6081/2021MT',
                database: process.env.POSTGRES_DB || 'medical_tracker',
                entities: [user_entity_1.User],
                synchronize: false,
                logging: false, // Disable verbose logging
            }),
            cache_module_1.CacheModule,
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
        ],
    })
], MinimalAppModule);
async function bootstrap() {
    console.log('🚀 Starting minimal backend server...');
    try {
        const app = await core_1.NestFactory.create(MinimalAppModule, {
            logger: ['error', 'warn', 'log'], // Reduce logging
        });
        // Enable CORS
        app.enableCors({
            origin: ['http://localhost:3000', 'http://localhost:3001'],
            credentials: true,
        });
        // Global prefix
        app.setGlobalPrefix('api/v1');
        // Global validation pipe
        app.useGlobalPipes(new common_2.ValidationPipe({
            whitelist: true,
            forbidNonWhitelisted: true,
            transform: true,
        }));
        const port = process.env.SERVER_PORT || 3002;
        await app.listen(port);
        console.log(`✅ Backend server is running on: http://localhost:${port}`);
        console.log(`📋 Available endpoints:`);
        console.log(`   POST http://localhost:${port}/api/v1/auth/login`);
        console.log(`   POST http://localhost:${port}/api/v1/auth/register`);
        console.log(`   GET  http://localhost:${port}/api/v1/auth/test-user`);
        console.log(`   GET  http://localhost:${port}/api/v1/users/profile`);
    }
    catch (error) {
        console.error('❌ Error starting server:', error);
        process.exit(1);
    }
}
bootstrap();
