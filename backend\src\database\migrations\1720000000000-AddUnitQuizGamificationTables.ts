import { MigrationInterface, QueryRunner, Table, TableForeignKey } from "typeorm";

export class AddUnitQuizGamificationTables1720000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // unit_quiz_scores
    await queryRunner.createTable(new Table({
      name: "unit_quiz_scores",
      columns: [
        { name: "id", type: "uuid", isPrimary: true, isGenerated: true, generationStrategy: "uuid" },
        { name: "userId", type: "uuid" },
        { name: "unitId", type: "uuid" },
        { name: "score", type: "int" },
        { name: "passed", type: "boolean", default: false },
        { name: "createdAt", type: "timestamp", default: "now()" },
        { name: "updatedAt", type: "timestamp", default: "now()" }
      ],
      uniques: [{ columnNames: ["userId", "unitId"] }]
    }));
    await queryRunner.createForeignKey("unit_quiz_scores", new TableForeignKey({
      columnNames: ["userId"],
      referencedColumnNames: ["id"],
      referencedTableName: "users",
      onDelete: "CASCADE"
    }));
    await queryRunner.createForeignKey("unit_quiz_scores", new TableForeignKey({
      columnNames: ["unitId"],
      referencedColumnNames: ["id"],
      referencedTableName: "units",
      onDelete: "CASCADE"
    }));

    // xp_logs
    await queryRunner.createTable(new Table({
      name: "xp_logs",
      columns: [
        { name: "id", type: "uuid", isPrimary: true, isGenerated: true, generationStrategy: "uuid" },
        { name: "userId", type: "uuid" },
        { name: "amount", type: "int" },
        { name: "reason", type: "varchar" },
        { name: "createdAt", type: "timestamp", default: "now()" }
      ]
    }));
    await queryRunner.createForeignKey("xp_logs", new TableForeignKey({
      columnNames: ["userId"],
      referencedColumnNames: ["id"],
      referencedTableName: "users",
      onDelete: "CASCADE"
    }));

    // badges
    await queryRunner.createTable(new Table({
      name: "badges",
      columns: [
        { name: "id", type: "uuid", isPrimary: true, isGenerated: true, generationStrategy: "uuid" },
        { name: "userId", type: "uuid" },
        { name: "badgeType", type: "varchar" },
        { name: "awardedAt", type: "timestamp", default: "now()" }
      ]
    }));
    await queryRunner.createForeignKey("badges", new TableForeignKey({
      columnNames: ["userId"],
      referencedColumnNames: ["id"],
      referencedTableName: "users",
      onDelete: "CASCADE"
    }));

    // streaks
    await queryRunner.createTable(new Table({
      name: "streaks",
      columns: [
        { name: "id", type: "uuid", isPrimary: true, isGenerated: true, generationStrategy: "uuid" },
        { name: "userId", type: "uuid" },
        { name: "currentStreak", type: "int", default: 0 },
        { name: "lastUpdated", type: "timestamp", default: "now()" }
      ]
    }));
    await queryRunner.createForeignKey("streaks", new TableForeignKey({
      columnNames: ["userId"],
      referencedColumnNames: ["id"],
      referencedTableName: "users",
      onDelete: "CASCADE"
    }));
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable("streaks");
    await queryRunner.dropTable("badges");
    await queryRunner.dropTable("xp_logs");
    await queryRunner.dropTable("unit_quiz_scores");
  }
} 