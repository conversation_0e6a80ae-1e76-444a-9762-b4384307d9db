import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, UpdateDateColumn } from 'typeorm';
import { User } from '../../entities/user.entity';

@Entity('streaks')
export class Streak {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, { eager: true })
  user: User;

  @Column('int', { default: 0 })
  currentStreak: number;

  @UpdateDateColumn()
  lastUpdated: Date;
} 