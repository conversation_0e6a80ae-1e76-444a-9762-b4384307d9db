import React, { useState } from 'react';
import Link from 'next/link';
import { Menu, X } from 'lucide-react';
import navigationConfig from './navigationConfig';

const MobileNav: React.FC = () => {
  const [open, setOpen] = useState(false);

  return (
    <>
      {/* Hamburger menu button */}
      <button
        className="lg:hidden fixed top-4 left-4 z-50 p-2 rounded-md text-gray-700 bg-white shadow hover:text-blue-600"
        onClick={() => setOpen(true)}
        aria-label="Open navigation menu"
      >
        <Menu className="h-6 w-6" />
      </button>

      {/* Mobile drawer */}
      <div
        className={`fixed inset-0 z-40 bg-white transition-transform duration-300 ease-in-out ${open ? 'translate-x-0' : '-translate-x-full'} lg:hidden`}
      >
        <div className="flex items-center justify-between p-4 border-b">
          <span className="text-xl font-bold text-blue-600">MedTrack</span>
          <button
            className="p-2 rounded hover:bg-gray-100"
            onClick={() => setOpen(false)}
            aria-label="Close navigation menu"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
        <nav className="flex flex-col p-4 space-y-2">
          {navigationConfig.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className="flex items-center px-3 py-2 text-base font-medium rounded hover:bg-blue-50 text-gray-700 hover:text-blue-700"
              onClick={() => setOpen(false)}
            >
              {item.icon && <item.icon className="mr-3 h-5 w-5" />}
              {item.label}
            </Link>
          ))}
        </nav>
      </div>
    </>
  );
};

export default MobileNav; 