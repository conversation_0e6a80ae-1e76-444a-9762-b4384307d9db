"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const users_repository_1 = require("./repositories/users.repository");
const cache_service_1 = require("../../cache/cache.service");
const user_entity_1 = require("../../entities/user.entity");
const bcryptjs = __importStar(require("bcryptjs"));
let UsersService = class UsersService {
    constructor(usersRepository, cacheService) {
        this.usersRepository = usersRepository;
        this.cacheService = cacheService;
        this.CACHE_PREFIX = 'user';
        this.CACHE_TTL = 3600; // 1 hour
        this.SALT_ROUNDS = 12; // Increased from 10 for better security
    }
    /** Fetch all users directly from the repository */
    async findAll() {
        return this.usersRepository.findAll();
    }
    /** Fetch a user by ID with caching */
    async findById(id) {
        if (!id) {
            throw new Error('User ID is required');
        }
        const cacheKey = this.cacheService.generateKey(this.CACHE_PREFIX, { id });
        const cachedUser = await this.cacheService.get(cacheKey);
        if (cachedUser) {
            return cachedUser;
        }
        const user = await this.usersRepository.findById(id);
        if (user) {
            await this.cacheService.set(cacheKey, user, this.CACHE_TTL);
        }
        return user;
    }
    /** Fetch a user by email with caching */
    async findByEmail(email) {
        if (!email) {
            throw new Error('Email is required');
        }
        console.log(`[USERS-SERVICE] Finding user by email: ${email}`);
        const cacheKey = this.cacheService.generateKey(this.CACHE_PREFIX, { email });
        const cachedUser = await this.cacheService.get(cacheKey);
        if (cachedUser) {
            console.log(`[USERS-SERVICE] User found in cache: ${email}`);
            return cachedUser;
        }
        console.log(`[USERS-SERVICE] User not in cache, querying database: ${email}`);
        const user = await this.usersRepository.findByEmail(email);
        if (user) {
            console.log(`[USERS-SERVICE] User found in database: ${email}, ID: ${user.id}`);
            await this.cacheService.set(cacheKey, user, this.CACHE_TTL);
            // Also cache by ID for consistency
            const idCacheKey = this.cacheService.generateKey(this.CACHE_PREFIX, { id: user.id });
            await this.cacheService.set(idCacheKey, user, this.CACHE_TTL);
        }
        else {
            console.log(`[USERS-SERVICE] User not found in database: ${email}`);
        }
        return user;
    }
    /** Create a new user with proper validation and password hashing */
    async create(emailOrUser, name, password) {
        console.log(`[USERS-SERVICE] Creating new user: ${typeof emailOrUser === 'string' ? emailOrUser : emailOrUser.email}`);
        let user;
        if (emailOrUser instanceof user_entity_1.User) {
            user = emailOrUser;
            console.log(`[USERS-SERVICE] Creating user from User object: ${user.email}`);
        }
        else {
            // Validate email format
            if (!this.isValidEmail(emailOrUser)) {
                throw new Error('Invalid email format');
            }
            // Check if user already exists
            const existingUser = await this.findByEmail(emailOrUser);
            if (existingUser) {
                throw new common_1.ConflictException('User with this email already exists');
            }
            console.log(`[USERS-SERVICE] Creating user from email: ${emailOrUser}, name: ${name}`);
            user = new user_entity_1.User();
            user.email = emailOrUser;
            user.name = name ?? '';
            user.username = await this.generateUniqueUsername(emailOrUser);
            if (!password) {
                console.error(`[USERS-SERVICE] Password missing for new user: ${emailOrUser}`);
                throw new Error('Password is required for new users');
            }
            // Validate password strength
            if (!this.isValidPassword(password)) {
                throw new Error('Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character');
            }
            console.log(`[USERS-SERVICE] Hashing password for new user: ${emailOrUser}`);
            user.password_hash = await bcryptjs.hash(password, this.SALT_ROUNDS);
            user.role = user_entity_1.UserRole.STUDENT;
            user.createdAt = new Date();
            user.updatedAt = new Date();
        }
        console.log(`[USERS-SERVICE] Saving user to database: ${user.email}`);
        const newUser = await this.usersRepository.create(user);
        console.log(`[USERS-SERVICE] User created with ID: ${newUser.id}`);
        // Cache by both ID and email
        await this.setCacheForUser(newUser);
        return newUser;
    }
    /** Update a user and invalidate related cache */
    async update(user) {
        const { id, ...userData } = user;
        if (!id) {
            throw new Error('User ID is required for update');
        }
        const existingUser = await this.usersRepository.findById(id);
        if (!existingUser) {
            throw new common_1.NotFoundException('User not found');
        }
        // Update timestamp
        userData.updatedAt = new Date();
        await this.usersRepository.update(id, userData);
        const updatedUser = await this.usersRepository.findById(id);
        if (!updatedUser) {
            throw new common_1.NotFoundException('User not found after update');
        }
        // Clear old cache entries and set new ones
        await this.clearCacheForUser(existingUser);
        await this.setCacheForUser(updatedUser);
        return updatedUser;
    }
    /** Delete a user and clear its cache */
    async delete(id) {
        if (!id) {
            throw new Error('User ID is required');
        }
        const user = await this.usersRepository.findById(id);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        await this.usersRepository.delete(id);
        await this.clearCacheForUser(user);
    }
    /** Fetch all users with caching */
    async getAllUsers() {
        const cacheKey = this.cacheService.generateKey(this.CACHE_PREFIX, { type: 'all_users' });
        const cachedUsers = await this.cacheService.get(cacheKey);
        if (cachedUsers) {
            return cachedUsers;
        }
        const users = await this.usersRepository.findAll();
        await this.cacheService.set(cacheKey, users, this.CACHE_TTL);
        return users;
    }
    /** Fetch a user by username with caching */
    async findByUsername(username) {
        if (!username) {
            throw new Error('Username is required');
        }
        const cacheKey = this.cacheService.generateKey(this.CACHE_PREFIX, { username });
        const cachedUser = await this.cacheService.get(cacheKey);
        if (cachedUser) {
            return cachedUser;
        }
        const user = await this.usersRepository.findByUsername(username);
        if (user) {
            await this.cacheService.set(cacheKey, user, this.CACHE_TTL);
        }
        return user;
    }
    /** Validate a user's credentials */
    async validateUser(username, password) {
        if (!username || !password) {
            throw new common_1.UnauthorizedException('Username and password are required');
        }
        const user = await this.findByUsername(username);
        if (!user) {
            throw new common_1.UnauthorizedException('Invalid username or password');
        }
        const isPasswordValid = await bcryptjs.compare(password, user.password_hash);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('Invalid username or password');
        }
        return user;
    }
    /** Reset a user's password */
    async resetPassword(email, newPassword) {
        if (!email || !newPassword) {
            throw new Error('Email and new password are required');
        }
        if (!this.isValidPassword(newPassword)) {
            throw new Error('Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character');
        }
        const user = await this.usersRepository.findByEmail(email);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const newPasswordHash = await bcryptjs.hash(newPassword, this.SALT_ROUNDS);
        await this.usersRepository.update(user.id, {
            password_hash: newPasswordHash,
            updatedAt: new Date()
        });
        // Clear cache to ensure fresh data
        await this.clearCacheForUser(user);
    }
    /** Update a user's password with validation */
    async updatePassword(userId, oldPassword, newPassword) {
        if (!userId || !oldPassword || !newPassword) {
            throw new Error('User ID, old password, and new password are required');
        }
        if (!this.isValidPassword(newPassword)) {
            throw new Error('New password must be at least 8 characters long and contain uppercase, lowercase, number, and special character');
        }
        const user = await this.usersRepository.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const isPasswordValid = await bcryptjs.compare(oldPassword, user.password_hash);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('Current password is incorrect');
        }
        const newPasswordHash = await bcryptjs.hash(newPassword, this.SALT_ROUNDS);
        await this.usersRepository.update(userId, {
            password_hash: newPasswordHash,
            updatedAt: new Date()
        });
        // Clear cache
        await this.clearCacheForUser(user);
    }
    /** Update a user's role */
    async updateUserRole(id, role) {
        if (!id || !role) {
            throw new Error('User ID and role are required');
        }
        const user = await this.usersRepository.findById(id);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        await this.usersRepository.update(id, {
            role,
            updatedAt: new Date()
        });
        const updatedUser = await this.usersRepository.findById(id);
        if (!updatedUser) {
            throw new common_1.NotFoundException('User not found after update');
        }
        // Update cache
        await this.clearCacheForUser(user);
        await this.setCacheForUser(updatedUser);
        return updatedUser;
    }
    /** Update user details */
    async updateUserDetails(id, userDetails) {
        if (!id) {
            throw new Error('User ID is required');
        }
        const user = await this.usersRepository.findById(id);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        // Prevent updating sensitive fields through this method
        const { password_hash, createdAt, ...safeDetails } = userDetails;
        safeDetails.updatedAt = new Date();
        await this.usersRepository.update(id, safeDetails);
        const updatedUser = await this.usersRepository.findById(id);
        if (!updatedUser) {
            throw new common_1.NotFoundException('User not found after update');
        }
        // Update cache
        await this.clearCacheForUser(user);
        await this.setCacheForUser(updatedUser);
        return updatedUser;
    }
    /** Clear cache for a specific user by ID */
    async clearCache(id) {
        if (!id)
            return;
        const user = await this.usersRepository.findById(id);
        if (user) {
            await this.clearCacheForUser(user);
        }
    }
    /** Clear all user-related cache */
    async clearAllCache() {
        await this.cacheService.clear(this.CACHE_PREFIX);
    }
    // Private helper methods
    async setCacheForUser(user) {
        const promises = [];
        // Cache by ID
        const idKey = this.cacheService.generateKey(this.CACHE_PREFIX, { id: user.id });
        promises.push(this.cacheService.set(idKey, user, this.CACHE_TTL));
        // Cache by email
        if (user.email) {
            const emailKey = this.cacheService.generateKey(this.CACHE_PREFIX, { email: user.email });
            promises.push(this.cacheService.set(emailKey, user, this.CACHE_TTL));
        }
        // Cache by username
        if (user.username) {
            const usernameKey = this.cacheService.generateKey(this.CACHE_PREFIX, { username: user.username });
            promises.push(this.cacheService.set(usernameKey, user, this.CACHE_TTL));
        }
        await Promise.all(promises);
    }
    async clearCacheForUser(user) {
        const promises = [];
        // Clear cache by ID
        const idKey = this.cacheService.generateKey(this.CACHE_PREFIX, { id: user.id });
        promises.push(this.cacheService.delete(idKey));
        // Clear cache by email
        if (user.email) {
            const emailKey = this.cacheService.generateKey(this.CACHE_PREFIX, { email: user.email });
            promises.push(this.cacheService.delete(emailKey));
        }
        // Clear cache by username
        if (user.username) {
            const usernameKey = this.cacheService.generateKey(this.CACHE_PREFIX, { username: user.username });
            promises.push(this.cacheService.delete(usernameKey));
        }
        // Clear all users cache as well
        const allUsersKey = this.cacheService.generateKey(this.CACHE_PREFIX, { type: 'all_users' });
        promises.push(this.cacheService.delete(allUsersKey));
        await Promise.all(promises);
    }
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    isValidPassword(password) {
        // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
        return passwordRegex.test(password);
    }
    async generateUniqueUsername(email) {
        const baseUsername = email.split('@')[0].toLowerCase().replace(/[^a-z0-9]/g, '');
        let username = baseUsername;
        let counter = 1;
        while (await this.findByUsername(username)) {
            username = `${baseUsername}${counter}`;
            counter++;
        }
        return username;
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [users_repository_1.UsersRepository,
        cache_service_1.CacheService])
], UsersService);
