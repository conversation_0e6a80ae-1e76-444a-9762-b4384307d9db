"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
// src/modules/auth/auth.service.ts
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../../entities/user.entity");
const security_entity_1 = require("../../entities/security.entity");
const security_service_1 = require("./security.service");
const refresh_token_service_1 = require("./refresh-token.service");
const token_blacklist_service_1 = require("./token-blacklist.service");
const bcrypt = __importStar(require("bcryptjs"));
const auth_types_1 = require("./types/auth.types");
let AuthService = AuthService_1 = class AuthService {
    constructor(userRepository, sessionRepository, jwtService, securityService, refreshTokenService, tokenBlacklistService) {
        this.userRepository = userRepository;
        this.sessionRepository = sessionRepository;
        this.jwtService = jwtService;
        this.securityService = securityService;
        this.refreshTokenService = refreshTokenService;
        this.tokenBlacklistService = tokenBlacklistService;
        this.logger = new common_1.Logger(AuthService_1.name);
        this.MAX_FAILED_ATTEMPTS = 5;
        this.LOCK_DURATION = 1000 * 60 * 60; // 1 hour
    }
    async login(loginRequest, ipAddress, userAgent) {
        const { email, password, deviceId, twoFactorToken } = loginRequest;
        const identifier = email || 'unknown';
        const logContext = { identifier, ipAddress, userAgent };
        try {
            // Find user by email first, then by username if email not provided
            let user = null;
            if (email) {
                user = await this.userRepository.findOne({ where: { email } });
            }
            // If no user found by email and we have a potential username, try username lookup
            if (!user && email) {
                user = await this.userRepository.findOne({ where: { username: email } });
            }
            if (!user) {
                this.logger.warn('Login attempt failed: User not found', logContext);
                await this.logFailedLogin(identifier, 'USER_NOT_FOUND', ipAddress);
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            // Check if account is locked
            if (user.is_locked && user.locked_until && user.locked_until > new Date()) {
                this.logger.warn('Login attempt failed: Account is locked', { ...logContext, userId: user.id });
                throw new common_1.UnauthorizedException('Account is locked. Please try again later.');
            }
            // Check if account is inactive
            if (!user.is_active) {
                this.logger.warn('Login attempt failed: Account is inactive', { ...logContext, userId: user.id });
                throw new common_1.UnauthorizedException('Account is inactive. Please check your email for activation instructions.');
            }
            // Verify password
            const isPasswordValid = await bcrypt.compare(password, user.password_hash);
            if (!isPasswordValid) {
                this.logger.warn('Login attempt failed: Invalid password', { ...logContext, userId: user.id });
                await this.logFailedLogin(email, 'INVALID_PASSWORD', ipAddress);
                // Increment failed attempts
                await this.userRepository.update(user.id, {
                    failed_login_attempts: user.failed_login_attempts + 1
                });
                // Check if we should lock the account
                if (user.failed_login_attempts + 1 >= this.MAX_FAILED_ATTEMPTS) {
                    await this.lockAccount(user.id);
                    throw new common_1.UnauthorizedException('Too many failed attempts. Account has been locked. Please try again later.');
                }
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            // Check if 2FA is required
            const securitySettings = await this.securityService.getSecuritySettings(user.id);
            if (securitySettings?.twoFactorEnabled && securitySettings.twoFactorSecret) {
                if (!twoFactorToken) {
                    this.logger.warn('Login attempt failed: 2FA token missing', { ...logContext, userId: user.id });
                    throw new common_1.BadRequestException('Two-factor authentication required');
                }
                const is2FAValid = await this.securityService.verifyTwoFactor(user.id, twoFactorToken);
                if (!is2FAValid) {
                    this.logger.warn('Login attempt failed: Invalid 2FA token', { ...logContext, userId: user.id });
                    await this.logFailedLogin(email, 'INVALID_2FA', ipAddress);
                    throw new common_1.UnauthorizedException('Invalid two-factor authentication token');
                }
            }
            // Clear failed login attempts on successful login
            await this.clearFailedLoginAttempts(email);
            // Generate tokens
            const payload = {
                sub: user.id,
                email: user.email,
                role: user.role,
            };
            const accessToken = this.jwtService.sign(payload);
            const refreshToken = await this.refreshTokenService.createRefreshToken(user.id);
            // Create session record
            await this.createUserSession(user.id, accessToken, deviceId, ipAddress, userAgent);
            // Log successful login
            this.logger.log('Login successful', { ...logContext, userId: user.id });
            await this.securityService.logSecurityEvent(user.id, auth_types_1.SECURITY_EVENT_TYPES.LOGIN_SUCCESS, {
                ipAddress,
                deviceId,
                userAgent,
                timestamp: new Date(),
            });
            // Check if email is verified
            const isEmailVerified = await this.securityService.isEmailVerified(user.id);
            return {
                accessToken,
                refreshToken,
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.first_name,
                    lastName: user.last_name,
                    role: user.role,
                    isEmailVerified,
                },
            };
        }
        catch (error) {
            if (error instanceof common_1.UnauthorizedException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error('Login error:', { ...logContext, error: error.message, stack: error.stack });
            throw new common_1.UnauthorizedException('Authentication failed');
        }
    }
    async register(registerRequest) {
        const { email, password, firstName, lastName, username, role } = registerRequest;
        // Check if user already exists by email
        const existingUserByEmail = await this.userRepository.findOne({ where: { email } });
        if (existingUserByEmail) {
            throw new common_1.BadRequestException('User with this email already exists');
        }
        // Check if username already exists (if provided)
        if (username) {
            const existingUserByUsername = await this.userRepository.findOne({ where: { username } });
            if (existingUserByUsername) {
                throw new common_1.BadRequestException('User with this username already exists');
            }
        }
        // Hash password
        const passwordHash = await bcrypt.hash(password, 12);
        // Create user
        const user = this.userRepository.create({
            email,
            username: username || email, // Use provided username or fallback to email
            password_hash: passwordHash,
            first_name: firstName,
            last_name: lastName,
            name: `${firstName} ${lastName}`.trim(),
            role: role === 'admin' ? user_entity_1.UserRole.ADMIN : (role === 'teacher' ? user_entity_1.UserRole.TEACHER : user_entity_1.UserRole.STUDENT),
            createdAt: new Date(),
            updatedAt: new Date()
        });
        const savedUser = await this.userRepository.save(user);
        await this.securityService.sendVerificationEmail(savedUser);
        // Generate tokens (user can use the app but with limited access until verified)
        const payload = {
            sub: savedUser.id,
            email: savedUser.email,
            role: savedUser.role,
        };
        const accessToken = this.jwtService.sign(payload);
        const refreshToken = await this.refreshTokenService.createRefreshToken(savedUser.id);
        return {
            accessToken,
            refreshToken,
            user: {
                id: savedUser.id,
                email: savedUser.email,
                firstName: savedUser.first_name,
                lastName: savedUser.last_name,
                role: savedUser.role,
                isEmailVerified: false,
            },
        };
    }
    async refreshToken(refreshToken, userId) {
        // Validate refresh token
        const isValid = await this.refreshTokenService.validateRefreshToken(refreshToken, userId);
        if (!isValid) {
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
        // Get user
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        // Generate new tokens
        const payload = {
            sub: user.id,
            email: user.email,
            role: user.role,
        };
        const newAccessToken = this.jwtService.sign(payload);
        // Revoke old refresh token and create new one
        await this.refreshTokenService.revokeRefreshToken(refreshToken);
        const newRefreshToken = await this.refreshTokenService.createRefreshToken(userId);
        return {
            accessToken: newAccessToken,
            refreshToken: newRefreshToken,
        };
    }
    async logout(userId, refreshToken) {
        // Revoke refresh token if provided
        if (refreshToken) {
            await this.refreshTokenService.revokeRefreshToken(refreshToken);
        }
        // Deactivate all user sessions
        await this.sessionRepository.update({ userId, isActive: true }, { isActive: false, updatedAt: new Date() });
        // Log logout event
        await this.securityService.logSecurityEvent(userId, auth_types_1.SECURITY_EVENT_TYPES.SESSION_REVOKED, {
            timestamp: new Date(),
        });
    }
    async logoutAllDevices(userId) {
        // Revoke all refresh tokens
        await this.refreshTokenService.revokeAllUserTokens(userId);
        // Deactivate all sessions
        await this.sessionRepository.update({ userId, isActive: true }, { isActive: false, updatedAt: new Date() });
        // Log the event
        await this.securityService.logSecurityEvent(userId, auth_types_1.SECURITY_EVENT_TYPES.SESSION_REVOKED, {
            timestamp: new Date(),
            allDevices: true,
        });
    }
    async createUserSession(userId, accessToken, deviceId, ipAddress, userAgent) {
        const session = this.sessionRepository.create({
            userId,
            deviceId,
            userAgent,
            ipAddress,
            lastAccessed: new Date(),
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
        });
        await this.sessionRepository.save(session);
    }
    async logFailedLogin(email, reason, ipAddress) {
        // Find user if exists to log the event
        const user = await this.userRepository.findOne({ where: { email } });
        if (user) {
            await this.securityService.logSecurityEvent(user.id, auth_types_1.SECURITY_EVENT_TYPES.LOGIN_FAILURE, {
                reason,
                ipAddress,
                timestamp: new Date(),
            });
        }
    }
    async validateToken(token) {
        try {
            const payload = this.jwtService.verify(token);
            const user = await this.userRepository.findOne({ where: { id: payload.sub } });
            if (!user) {
                throw new common_1.UnauthorizedException('User not found');
            }
            return {
                id: user.id,
                email: user.email,
                role: user.role,
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid token');
        }
    }
    async validateUser(email, password) {
        const user = await this.userRepository.findOne({ where: { email } });
        if (!user) {
            return null;
        }
        const isPasswordValid = await bcrypt.compare(password, user.password_hash);
        if (!isPasswordValid) {
            return null;
        }
        const { password_hash, ...result } = user;
        return result;
    }
    async lockAccount(userId) {
        await this.userRepository.update(userId, {
            is_locked: true,
            locked_until: new Date(Date.now() + this.LOCK_DURATION),
        });
    }
    async clearFailedLoginAttempts(email) {
        await this.userRepository.update({ email }, {
            failed_login_attempts: 0,
            is_locked: false,
            locked_until: new Date(0) // Set to epoch time (1970-01-01) to indicate no lock
        });
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(security_entity_1.UserSession)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        jwt_1.JwtService,
        security_service_1.SecurityService,
        refresh_token_service_1.RefreshTokenService,
        token_blacklist_service_1.TokenBlacklistService])
], AuthService);
