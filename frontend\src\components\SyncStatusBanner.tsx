'use client';

import { useEffect, useState } from 'react';
import { syncService } from '@/lib/offline/syncService';
import { AlertCircle, CheckCircle2, Clock } from 'lucide-react';

interface SyncStatus {
  lastSyncTimestamp: number;
  isOnline: boolean;
  pendingChanges: number;
}

export function SyncStatusBanner() {
  const [status, setStatus] = useState<SyncStatus | null>(null);

  useEffect(() => {
    const updateStatus = async () => {
      const currentStatus: SyncStatus = await syncService.getSyncStatus();
      setStatus(currentStatus);
    };

    updateStatus();
    const interval = setInterval(updateStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  if (!status) return null;

  const getStatusColor = () => {
    if (!status.isOnline) return 'bg-yellow-100 text-yellow-800';
    if (status.pendingChanges > 0) return 'bg-blue-100 text-blue-800';
    return 'bg-green-100 text-green-800';
  };

  const getStatusIcon = () => {
    if (!status.isOnline) return <AlertCircle className="h-5 w-5" />;
    if (status.pendingChanges > 0) return <Clock className="h-5 w-5" />;
    return <CheckCircle2 className="h-5 w-5" />;
  };

  const getStatusMessage = () => {
    if (!status.isOnline) return 'You are offline. Changes will sync when you reconnect.';
    if (status.pendingChanges > 0) return `${status.pendingChanges} changes pending sync`;
    return 'All changes synced';
  };

  return (
    <div className={`fixed bottom-0 left-0 right-0 p-2 ${getStatusColor()} transition-colors duration-300`}>
      <div className="container mx-auto flex items-center justify-center gap-2">
        {getStatusIcon()}
        <span className="text-sm font-medium">{getStatusMessage()}</span>
      </div>
    </div>
  );
}