import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Unit } from '../../entities/unit.entity';
import { QuizQuestion } from '../../entities/quiz-question.entity';

@Entity('unit_quizzes')
export class UnitQuiz {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Unit, unit => unit.unitQuizzes)
  unit: Unit;

  @Column()
  title: string;

  @Column('text')
  instructions: string;

  @OneToMany(() => QuizQuestion, (q: QuizQuestion) => q.unitQuiz, { cascade: true })
  questions: QuizQuestion[];

  @Column({ type: 'boolean', default: false })
  isPublished: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 