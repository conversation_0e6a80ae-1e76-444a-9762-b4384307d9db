"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheService = void 0;
// src/cache/cache.service.ts
const common_1 = require("@nestjs/common");
const cache_manager_1 = require("@nestjs/cache-manager");
let CacheService = class CacheService {
    constructor(cacheManager) {
        this.cacheManager = cacheManager;
    }
    async get(key) {
        const value = await this.cacheManager.get(key);
        return value ?? undefined;
    }
    async clear(prefix) {
        // This is the safer approach - clear the entire cache
        // Redis-specific key deletion would require direct Redis client access
        await this.cacheManager.clear();
    }
    async mget(keys) {
        // Handle the case where mget might not be supported by the store
        try {
            const values = await this.cacheManager.mget(keys);
            return values.map((value) => (value !== null ? value : undefined));
        }
        catch (error) {
            // Fallback to individual gets if mget is not supported
            return Promise.all(keys.map(async (key) => this.get(key)));
        }
    }
    async set(key, value, ttl) {
        await this.cacheManager.set(key, value, ttl);
    }
    async delete(key) {
        await this.cacheManager.del(key);
    }
    async reset() {
        await this.cacheManager.clear();
    }
    async keys(pattern) {
        // Note: This requires Redis store. For other stores, you might need a different implementation
        const cacheManager = this.cacheManager;
        if (cacheManager.store?.getClient) {
            const client = await cacheManager.store.getClient();
            return client.keys(pattern);
        }
        return [];
    }
    generateKey(prefix, params) {
        const sortedParams = Object.keys(params)
            .sort()
            .reduce((acc, key) => {
            acc[key] = params[key];
            return acc;
        }, {});
        return `${prefix}:${JSON.stringify(sortedParams)}`;
    }
};
exports.CacheService = CacheService;
exports.CacheService = CacheService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [Object])
], CacheService);
