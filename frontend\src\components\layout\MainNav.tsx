import React from 'react';
import Link from 'next/link';
import navigationConfig from './navigationConfig';

const MainNav: React.FC = () => {
  return (
    <nav className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col bg-white border-r border-gray-200 z-30">
      <div className="flex items-center h-16 px-4 border-b">
        <span className="text-2xl font-bold text-blue-600">MedTrack</span>
      </div>
      <div className="flex-1 flex flex-col py-4">
        {navigationConfig.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className="flex items-center px-4 py-2 text-base font-medium rounded hover:bg-blue-50 text-gray-700 hover:text-blue-700"
          >
            {item.icon && <item.icon className="mr-3 h-5 w-5" />}
            {item.label}
          </Link>
        ))}
      </div>
    </nav>
  );
};

export default MainNav; 