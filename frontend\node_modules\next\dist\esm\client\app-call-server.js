import { startTransition } from 'react';
import { ACTION_SERVER_ACTION } from './components/router-reducer/router-reducer-types';
import { dispatchAppRouterAction } from './components/use-action-queue';
export async function callServer(actionId, actionArgs) {
    return new Promise((resolve, reject)=>{
        startTransition(()=>{
            dispatchAppRouterAction({
                type: ACTION_SERVER_ACTION,
                actionId,
                actionArgs,
                resolve,
                reject
            });
        });
    });
}

//# sourceMappingURL=app-call-server.js.map