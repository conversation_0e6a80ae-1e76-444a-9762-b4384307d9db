console.log('Node.js is working');
console.log('Current directory:', process.cwd());
console.log('Node version:', process.version);

// Test if we can import basic modules
try {
  const fs = require('fs');
  console.log('fs module loaded successfully');
  
  const path = require('path');
  console.log('path module loaded successfully');
  
  // Check if package.json exists
  if (fs.existsSync('./package.json')) {
    console.log('package.json found');
    const pkg = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
    console.log('Project name:', pkg.name);
  } else {
    console.log('package.json not found');
  }
  
} catch (error) {
  console.error('Error:', error.message);
}
