import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, OneToMany, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Material } from './materials.entity';
import { Progress } from './progress.entity';
import { Topic } from './topic.entity';
import { UnitQuiz } from './unit-quiz.entity';

@Entity('units')
export class Unit {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    title: string;

    @Column('text')
    description: string;

    @Column()
    order_index: number;

    @ManyToOne(() => Topic, (topic: Topic) => topic.units, { nullable: true })
    topic: Topic;

    @OneToMany(() => Material, (material: Material) => material.unit)
    materials: Material[];

    @OneToMany(() => Progress, (progress: Progress) => progress.unit)
    progress: Progress[];

    @OneToMany(() => UnitQuiz, (quiz: UnitQuiz) => quiz.unit)
    unitQuizzes: UnitQuiz[];

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}