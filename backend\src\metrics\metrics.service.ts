import { Injectable } from '@nestjs/common';
// import { Counter, Gauge, Registry } from 'prom-client';

@Injectable()
export class MetricsService {
  private register: any; // Registry;
  private requestCounter: any; // Counter;
  private errorCounter: any; // Counter;
  private responseTimeHistogram: any; // Gauge;
  private activeConnections: any; // Gauge;
  private memoryUsage: any; // Gauge;

  constructor() {
    // Temporarily disable metrics initialization
    console.log('MetricsService: Prometheus metrics temporarily disabled');

    // Create a new registry
    // this.register = new Registry();

    // Request counter
    // this.requestCounter = new Counter({
    //   name: 'medtrack_http_requests_total',
    //   help: 'Total number of HTTP requests',
    //   labelNames: ['method', 'path', 'status'],
    //   registers: [this.register],
    // });

    // Error counter
    // this.errorCounter = new Counter({
    //   name: 'medtrack_http_errors_total',
    //   help: 'Total number of HTTP errors',
    //   labelNames: ['method', 'path', 'status', 'error'],
    //   registers: [this.register],
    // });

    // Response time histogram
    // this.responseTimeHistogram = new Gauge({
    //   name: 'medtrack_http_response_time_seconds',
    //   help: 'HTTP response time in seconds',
    //   labelNames: ['method', 'path'],
    //   registers: [this.register],
    // });

    // Active connections gauge
    // this.activeConnections = new Gauge({
    //   name: 'medtrack_active_connections',
    //   help: 'Number of active connections',
    //   registers: [this.register],
    // });

    // Memory usage gauge
    // this.memoryUsage = new Gauge({
    //   name: 'medtrack_memory_usage_bytes',
    //   help: 'Process memory usage in bytes',
    //   registers: [this.register],
    //   collect() {
    //     const usage = process.memoryUsage();
    //     this.set(usage.heapUsed);
    //   },
    // });
  }

  // Increment request counter
  incrementRequestCounter(method: string, path: string, status: number): void {
    // this.requestCounter.inc({ method, path, status });
    console.log(`Request: ${method} ${path} - ${status}`);
  }

  // Increment error counter
  incrementErrorCounter(method: string, path: string, status: number, error: string): void {
    // this.errorCounter.inc({ method, path, status, error });
    console.log(`Error: ${method} ${path} - ${status} - ${error}`);
  }

  // Record response time
  recordResponseTime(method: string, path: string, time: number): void {
    // this.responseTimeHistogram.set({ method, path }, time);
    console.log(`Response time: ${method} ${path} - ${time}ms`);
  }

  // Update active connections
  setActiveConnections(count: number): void {
    // this.activeConnections.set(count);
    console.log(`Active connections: ${count}`);
  }

  // Get all metrics
  async getMetrics(): Promise<string> {
    // return this.register.metrics();
    return 'Metrics temporarily disabled - install prom-client package';
  }
}
