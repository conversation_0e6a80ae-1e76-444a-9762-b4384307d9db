'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { apiService } from '@/services/api';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/Button'; // Standardized casing
import { toast } from 'sonner';
import { Trophy, Star, Target, Zap, BookOpen, Clock } from 'lucide-react';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  progress: number;
  completed: boolean;
  completedAt?: string;
  type: 'quiz' | 'study' | 'streak' | 'milestone';
  points: number;
}

interface AchievementStats {
  totalAchievements: number;
  completedAchievements: number;
  totalPoints: number;
  rank: string;
  nextRank: {
    name: string;
    pointsNeeded: number;
  };
}

export default function AchievementsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [stats, setStats] = useState<AchievementStats>({
    totalAchievements: 0,
    completedAchievements: 0,
    totalPoints: 0,
    rank: 'Beginner',
    nextRank: {
      name: 'Intermediate',
      pointsNeeded: 100,
    },
  });

  useEffect(() => {
    const fetchAchievements = async () => {
      try {
        const [achievementsResponse, statsResponse] = await Promise.all([
          apiService.get<Achievement[]>('/users/achievements'),
          apiService.get<AchievementStats>('/users/achievement-stats'),
        ]);
        setAchievements(achievementsResponse.data);
        setStats(statsResponse.data);
      } catch (error) {
        toast.error('Failed to load achievements');
      } finally {
        setLoading(false);
      }
    };

    fetchAchievements();
  }, []);

  const getAchievementIcon = (type: Achievement['type']) => {
    switch (type) {
      case 'quiz':
        return <Target className="w-8 h-8 text-blue-500" />;
      case 'study':
        return <BookOpen className="w-8 h-8 text-green-500" />;
      case 'streak':
        return <Zap className="w-8 h-8 text-yellow-500" />;
      case 'milestone':
        return <Star className="w-8 h-8 text-purple-500" />;
      default:
        return <Trophy className="w-8 h-8 text-gray-500" />;
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <ErrorBoundary>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Achievements</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card className="p-4">
            <div className="flex items-center space-x-4">
              <Trophy className="w-8 h-8 text-yellow-500" />
              <div>
                <p className="text-sm text-gray-500">Total Points</p>
                <p className="text-2xl font-bold">{stats.totalPoints}</p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center space-x-4">
              <Star className="w-8 h-8 text-blue-500" />
              <div>
                <p className="text-sm text-gray-500">Current Rank</p>
                <p className="text-2xl font-bold">{stats.rank}</p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center space-x-4">
              <Target className="w-8 h-8 text-green-500" />
              <div>
                <p className="text-sm text-gray-500">Achievements</p>
                <p className="text-2xl font-bold">
                  {stats.completedAchievements}/{stats.totalAchievements}
                </p>
              </div>
            </div>
          </Card>
          <Card className="p-4">
            <div className="flex items-center space-x-4">
              <Clock className="w-8 h-8 text-purple-500" />
              <div>
                <p className="text-sm text-gray-500">Next Rank</p>
                <p className="text-2xl font-bold">{stats.nextRank.name}</p>
                <p className="text-sm text-gray-500">
                  {stats.nextRank.pointsNeeded} points needed
                </p>
              </div>
            </div>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {achievements.map((achievement) => (
            <Card
              key={achievement.id}
              className={`p-6 ${
                achievement.completed ? 'border-green-500' : 'border-gray-200'
              }`}
            >
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  {getAchievementIcon(achievement.type)}
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold">{achievement.title}</h3>
                  <p className="text-sm text-gray-500 mt-1">
                    {achievement.description}
                  </p>
                  <div className="mt-4">
                    <div className="flex justify-between text-sm text-gray-500 mb-1">
                      <span>Progress</span>
                      <span>{achievement.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ width: `${achievement.progress}%` }}
                      />
                    </div>
                  </div>
                  {achievement.completed && (
                    <div className="mt-4 flex items-center text-sm text-green-500">
                      <Trophy className="w-4 h-4 mr-1" />
                      Completed
                      {achievement.completedAt && (
                        <span className="ml-2 text-gray-500">
                          on {new Date(achievement.completedAt).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  )}
                  <div className="mt-4 flex items-center text-sm text-gray-500">
                    <Star className="w-4 h-4 mr-1" />
                    {achievement.points} points
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {achievements.length === 0 && (
          <Card className="p-6 text-center">
            <p className="text-gray-500">No achievements found</p>
            <Button
              className="mt-4"
              onClick={() => router.push('/courses')}
            >
              Start Learning
            </Button>
          </Card>
        )}
      </div>
    </ErrorBoundary>
  );
}