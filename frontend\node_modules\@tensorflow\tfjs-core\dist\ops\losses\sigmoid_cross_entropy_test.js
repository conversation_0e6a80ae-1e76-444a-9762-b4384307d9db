/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../../index';
import { ALL_ENVS, describeWithFlags } from '../../jasmine_util';
import { expectArraysClose } from '../../test_util';
describeWithFlags('sigmoidCrossEntropy', ALL_ENVS, () => {
    it('All wrong', async () => {
        const label = tf.tensor2d([[0, 0, 1], [1, 0, 0], [0, 1, 0]], [3, 3]);
        const predictions = tf.tensor2d([[10.0, -10.0, -10.0], [-10.0, 10.0, -10.0], [-10.0, -10.0, 10.0]], [3, 3]);
        const y = tf.losses.sigmoidCrossEntropy(label, predictions);
        expect(y.shape).toEqual([]);
        expectArraysClose(await y.data(), 6.6667123);
    });
    it('All right', async () => {
        const label = tf.tensor2d([[1, 0, 0], [0, 1, 0], [0, 0, 1]], [3, 3]);
        const predictions = tf.tensor2d([[10.0, -10.0, -10.0], [-10.0, 10.0, -10.0], [-10.0, -10.0, 10.0]], [3, 3]);
        const y = tf.losses.sigmoidCrossEntropy(label, predictions);
        expect(y.shape).toEqual([]);
        expectArraysClose(await y.data(), 0);
    });
    it('Weighted - Reduction.SUM_BY_NONZERO_WEIGHTS', async () => {
        const label = tf.tensor2d([[0, 0, 1], [1, 0, 0], [0, 1, 0]], [3, 3]);
        const predictions = tf.tensor2d([[10.0, -10.0, -10.0], [-10.0, 10.0, -10.0], [-10.0, -10.0, 10.0]], [3, 3]);
        const weights = tf.tensor2d([[0.1, 0.2, 0.3]]);
        const y = tf.losses.sigmoidCrossEntropy(label, predictions, weights);
        expect(y.shape).toEqual([]);
        expectArraysClose(await y.data(), 1.3333424);
    });
    it('Weighted - Reduction.NONE', async () => {
        const label = tf.tensor2d([[0, 0, 1], [1, 0, 0], [0, 1, 0]], [3, 3]);
        const predictions = tf.tensor2d([[10.0, -10.0, -10.0], [-10.0, 10.0, -10.0], [-10.0, -10.0, 10.0]], [3, 3]);
        const weights = tf.tensor2d([[0.1, 0.2, 0.3]]);
        const y = tf.losses.sigmoidCrossEntropy(label, predictions, weights, undefined, tf.Reduction.NONE);
        expect(y.shape).toEqual([3, 3]);
        expectArraysClose(await y.data(), [
            1.0000046, 9.0797803e-06, 3.0000138e+00, 1.0000046e+00, 2.0000093e+00,
            1.3619671e-05, 4.5398901e-06, 2.0000093e+00, 3.0000138e+00
        ]);
    });
    it('Reduction.MEAN', async () => {
        const label = tf.tensor2d([[0, 0, 1], [1, 0, 0], [0, 1, 0]], [3, 3]);
        const predictions = tf.tensor2d([[10.0, -10.0, -10.0], [-10.0, 10.0, -10.0], [-10.0, -10.0, 10.0]], [3, 3]);
        const y = tf.losses.sigmoidCrossEntropy(label, predictions, undefined, undefined, tf.Reduction.MEAN);
        expect(y.shape).toEqual([]);
        expectArraysClose(await y.data(), 6.6667123);
    });
    it('Weighted - Reduction.MEAN', async () => {
        const label = tf.tensor2d([[0, 0, 1], [1, 0, 0], [0, 1, 0]], [3, 3]);
        const predictions = tf.tensor2d([[10.0, -10.0, -10.0], [-10.0, 10.0, -10.0], [-10.0, -10.0, 10.0]], [3, 3]);
        const weights = tf.tensor2d([[0.1, 0.2, 0.3]]);
        const y = tf.losses.sigmoidCrossEntropy(label, predictions, weights, undefined, tf.Reduction.MEAN);
        expect(y.shape).toEqual([]);
        expectArraysClose(await y.data(), 6.666712284088135);
    });
    it('Label Smoothing - Weighted - Reduction.MEAN', async () => {
        const label = tf.tensor2d([[0, 0, 1], [1, 0, 0], [0, 1, 0]], [3, 3]);
        const predictions = tf.tensor2d([[10.0, -10.0, -10.0], [-10.0, 10.0, -10.0], [-10.0, -10.0, 10.0]], [3, 3]);
        const weights = tf.tensor2d([[0.1, 0.2, 0.3]]);
        const labelSmoothing = 0.3;
        const y = tf.losses.sigmoidCrossEntropy(label, predictions, weights, labelSmoothing, tf.Reduction.MEAN);
        expect(y.shape).toEqual([]);
        expectArraysClose(await y.data(), 6.1667128);
    });
    it('throws when multiClassLabels and logits are of different shapes', () => {
        const multiClassLabels = tf.tensor2d([10, 10, 10, 10, 10, 10, 10, 10, 10], [3, 3]);
        const logits = tf.tensor2d([10, 10, 10, 10, 10, 10], [2, 3]);
        const e = new RegExp('Error in sigmoidCrossEntropy:  Shapes 3,3 and 2,3 must match');
        expect(() => tf.losses.sigmoidCrossEntropy(multiClassLabels, logits))
            .toThrowError(e);
    });
    it('throws when passed multiClassLabels as a non-tensor', () => {
        const predictions = tf.tensor2d([[10.0, -10.0, -10.0], [-10.0, 10.0, -10.0], [-10.0, -10.0, 10.0]], [3, 3]);
        const weights = tf.tensor2d([[0.1, 0.2, 0.3]]);
        const e = new RegExp('Argument \'multiClassLabels\' passed to \'sigmoidCrossEntropy\' ' +
            'must be a Tensor');
        expect(() => tf.losses.sigmoidCrossEntropy({}, predictions, weights, tf.Reduction.MEAN))
            .toThrowError(e);
    });
    it('throws when passed logits as a non-tensor', () => {
        const label = tf.tensor2d([[0, 0, 1], [1, 0, 0], [0, 1, 0]], [3, 3]);
        const weights = tf.tensor2d([[0.1, 0.2, 0.3]]);
        const e = new RegExp('Argument \'logits\' passed to \'sigmoidCrossEntropy\' ' +
            'must be a Tensor');
        expect(() => tf.losses.sigmoidCrossEntropy(label, {}, weights, tf.Reduction.MEAN))
            .toThrowError(e);
    });
    it('throws when passed weights as a non-tensor', () => {
        const label = tf.tensor2d([[0, 0, 1], [1, 0, 0], [0, 1, 0]], [3, 3]);
        const predictions = tf.tensor2d([[10.0, -10.0, -10.0], [-10.0, 10.0, -10.0], [-10.0, -10.0, 10.0]], [3, 3]);
        const e = /Argument 'weights' passed to 'sigmoidCrossEntropy' must be a Tensor/;
        expect(() => tf.losses.sigmoidCrossEntropy(label, predictions, {}, tf.Reduction.MEAN))
            .toThrowError(e);
    });
});
//# sourceMappingURL=data:application/json;base64,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