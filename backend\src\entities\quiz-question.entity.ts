import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { UnitQuiz } from './unit-quiz.entity';
import { Unit } from './unit.entity';

@Entity('quiz_questions')
export class QuizQuestion {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column('text')
    text: string;

    @ManyToOne(() => UnitQuiz, quiz => quiz.questions, { nullable: true })
    unitQuiz: UnitQuiz;

    @ManyToOne(() => Unit)
    unit: Unit;

    @Column('text')
    question_text: string;

    @Column('jsonb')
    options: string[];

    @Column('text')
    correct_answer: string;

    @Column('text', { nullable: true })
    explanation: string;

    @CreateDateColumn()
    created_at: Date;

    @UpdateDateColumn()
    updated_at: Date;
}