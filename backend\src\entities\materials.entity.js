"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Material = exports.ContentSource = exports.MaterialType = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("./user.entity");
const unit_entity_1 = require("./unit.entity");
const material_shares_entity_1 = require("./material_shares.entity");
const progress_entity_1 = require("./progress.entity");
var MaterialType;
(function (MaterialType) {
    MaterialType["EXAM_PREP"] = "exam_prep";
    MaterialType["CLINICAL_UPDATE"] = "clinical_update";
    MaterialType["GUIDELINE"] = "guideline";
    MaterialType["CASE_STUDY"] = "case_study";
    MaterialType["QUIZ"] = "quiz";
    MaterialType["ARTICLE"] = "article";
    MaterialType["VIDEO"] = "video";
    MaterialType["PRESENTATION"] = "presentation";
    MaterialType["WORKSHOP"] = "workshop";
    MaterialType["CONFERENCE"] = "conference";
    MaterialType["RESEARCH"] = "research";
})(MaterialType || (exports.MaterialType = MaterialType = {}));
var ContentSource;
(function (ContentSource) {
    ContentSource["KENYA_GUIDELINES"] = "kenya_guidelines";
    ContentSource["KEMRI"] = "kemri";
    ContentSource["WHO"] = "who";
    ContentSource["LOCAL"] = "local";
    ContentSource["INTERNATIONAL"] = "international";
})(ContentSource || (exports.ContentSource = ContentSource = {}));
let Material = class Material {
};
exports.Material = Material;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Material.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Material.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], Material.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: MaterialType, nullable: true }),
    __metadata("design:type", String)
], Material.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], Material.prototype, "content", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Material.prototype, "file_url", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Material.prototype, "answer", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], Material.prototype, "is_exam_focused", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], Material.prototype, "is_clinical_update", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: ContentSource, default: ContentSource.LOCAL }),
    __metadata("design:type", String)
], Material.prototype, "source", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], Material.prototype, "is_cpd_eligible", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Material.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Material.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float', nullable: true }),
    __metadata("design:type", Number)
], Material.prototype, "difficulty", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.created_materials),
    __metadata("design:type", user_entity_1.User)
], Material.prototype, "author", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => unit_entity_1.Unit, (unit) => unit.materials),
    __metadata("design:type", unit_entity_1.Unit)
], Material.prototype, "unit", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    __metadata("design:type", user_entity_1.User)
], Material.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => material_shares_entity_1.MaterialShare, (share) => share.material),
    __metadata("design:type", Array)
], Material.prototype, "shares", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => progress_entity_1.Progress, (progress) => progress.material),
    __metadata("design:type", Array)
], Material.prototype, "progress", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Material.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Material.prototype, "updatedAt", void 0);
exports.Material = Material = __decorate([
    (0, typeorm_1.Entity)('materials')
], Material);
