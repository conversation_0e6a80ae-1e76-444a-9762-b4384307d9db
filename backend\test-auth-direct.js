const { Client } = require('pg');
const bcrypt = require('bcryptjs');

async function testAuthentication() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'medical',
    password: 'AU110s/6081/2021MT',
    database: 'medical_tracker',
  });

  try {
    console.log('🔌 Connecting to PostgreSQL...');
    await client.connect();
    console.log('✅ Connected to PostgreSQL');

    // Test 1: Check if test user exists
    console.log('\n📋 Test 1: Checking if test user exists...');
    const userQuery = 'SELECT * FROM "user" WHERE username = $1 OR email = $2';
    const userResult = await client.query(userQuery, ['test', '<EMAIL>']);
    
    if (userResult.rows.length === 0) {
      console.log('❌ Test user not found');
      return;
    }
    
    const user = userResult.rows[0];
    console.log('✅ Test user found:', {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      is_locked: user.is_locked,
      failed_login_attempts: user.failed_login_attempts
    });

    // Test 2: Test password verification
    console.log('\n🔐 Test 2: Testing password verification...');
    const testPassword = 'test';
    const isPasswordValid = await bcrypt.compare(testPassword, user.password_hash);
    
    if (isPasswordValid) {
      console.log('✅ Password verification successful');
    } else {
      console.log('❌ Password verification failed');
      console.log('Expected password:', testPassword);
      console.log('Stored hash:', user.password_hash);
    }

    // Test 3: Test login logic simulation
    console.log('\n🚀 Test 3: Simulating login logic...');
    
    // Step 1: Find user by email
    let loginUser = null;
    const emailQuery = 'SELECT * FROM "user" WHERE email = $1';
    const emailResult = await client.query(emailQuery, ['test']);
    
    if (emailResult.rows.length > 0) {
      loginUser = emailResult.rows[0];
      console.log('✅ User found by email lookup');
    } else {
      console.log('⚠️ User not found by email, trying username...');
      
      // Step 2: Find user by username
      const usernameQuery = 'SELECT * FROM "user" WHERE username = $1';
      const usernameResult = await client.query(usernameQuery, ['test']);
      
      if (usernameResult.rows.length > 0) {
        loginUser = usernameResult.rows[0];
        console.log('✅ User found by username lookup');
      } else {
        console.log('❌ User not found by username either');
        return;
      }
    }

    // Step 3: Check if account is locked
    if (loginUser.is_locked) {
      if (loginUser.locked_until && new Date() < new Date(loginUser.locked_until)) {
        console.log('❌ Account is locked until:', loginUser.locked_until);
        return;
      } else {
        console.log('⚠️ Account was locked but lock has expired, unlocking...');
        await client.query(
          'UPDATE "user" SET is_locked = false, locked_until = NULL, failed_login_attempts = 0 WHERE id = $1',
          [loginUser.id]
        );
      }
    }

    // Step 4: Verify password
    const passwordMatch = await bcrypt.compare(testPassword, loginUser.password_hash);
    
    if (passwordMatch) {
      console.log('✅ Login successful!');
      
      // Reset failed attempts and update last login
      await client.query(
        'UPDATE "user" SET failed_login_attempts = 0, last_login = NOW() WHERE id = $1',
        [loginUser.id]
      );
      
      console.log('✅ User login data updated');
      
      // Return user data (simulating JWT token creation)
      const loginResponse = {
        user: {
          id: loginUser.id,
          username: loginUser.username,
          email: loginUser.email,
          firstName: loginUser.first_name,
          lastName: loginUser.last_name,
          role: loginUser.role
        },
        message: 'Login successful'
      };
      
      console.log('📋 Login response:', JSON.stringify(loginResponse, null, 2));
      
    } else {
      console.log('❌ Password verification failed');
      
      // Increment failed attempts
      const newFailedAttempts = (loginUser.failed_login_attempts || 0) + 1;
      const maxAttempts = 5;
      
      if (newFailedAttempts >= maxAttempts) {
        const lockUntil = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
        await client.query(
          'UPDATE "user" SET failed_login_attempts = $1, is_locked = true, locked_until = $2 WHERE id = $3',
          [newFailedAttempts, lockUntil, loginUser.id]
        );
        console.log('🔒 Account locked due to too many failed attempts');
      } else {
        await client.query(
          'UPDATE "user" SET failed_login_attempts = $1 WHERE id = $2',
          [newFailedAttempts, loginUser.id]
        );
        console.log(`⚠️ Failed attempt ${newFailedAttempts}/${maxAttempts}`);
      }
    }

    console.log('\n✅ Authentication test completed successfully');

  } catch (error) {
    console.error('❌ Error during authentication test:', error);
  } finally {
    await client.end();
    console.log('✅ Database connection closed');
  }
}

testAuthentication();
