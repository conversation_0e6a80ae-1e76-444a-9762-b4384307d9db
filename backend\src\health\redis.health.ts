import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
// import { InjectRedis } from '@nestjs/redis';
// import { Redis } from 'ioredis';

@Injectable()
export class RedisHealthIndicator extends HealthIndicator {
  constructor(/* @InjectRedis() private readonly redis: Redis */) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      // Temporarily disable Redis health check
      // await this.redis.ping();
      return this.getStatus(key, true, { message: 'Redis check temporarily disabled' });
    } catch (error) {
      throw new HealthCheckError(
        'Redis check failed',
        this.getStatus(key, false, { message: error.message })
      );
    }
  }
}
