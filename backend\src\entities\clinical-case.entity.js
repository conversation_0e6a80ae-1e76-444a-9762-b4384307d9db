"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateClinicalCaseDto = exports.ClinicalCase = void 0;
// src/entities/clinical-case.entity.ts
const typeorm_1 = require("typeorm");
const user_entity_1 = require("./user.entity");
const unit_entity_1 = require("./unit.entity");
let ClinicalCase = class ClinicalCase {
};
exports.ClinicalCase = ClinicalCase;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], ClinicalCase.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ClinicalCase.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], ClinicalCase.prototype, "patient_history", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], ClinicalCase.prototype, "clinical_findings", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { nullable: true }),
    __metadata("design:type", String)
], ClinicalCase.prototype, "lab_results", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { nullable: true }),
    __metadata("design:type", String)
], ClinicalCase.prototype, "imaging_results", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], ClinicalCase.prototype, "diagnosis", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], ClinicalCase.prototype, "treatment", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], ClinicalCase.prototype, "outcome_and_follow_up", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], ClinicalCase.prototype, "learning_points", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true }),
    __metadata("design:type", Object)
], ClinicalCase.prototype, "questions", void 0);
__decorate([
    (0, typeorm_1.Column)('boolean', { default: false }),
    __metadata("design:type", Boolean)
], ClinicalCase.prototype, "is_published", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { default: 0 }),
    __metadata("design:type", Number)
], ClinicalCase.prototype, "view_count", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.created_clinical_cases),
    __metadata("design:type", user_entity_1.User)
], ClinicalCase.prototype, "author", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => unit_entity_1.Unit),
    (0, typeorm_1.JoinTable)({
        name: 'clinical_case_units',
        joinColumn: { name: 'clinical_case_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'unit_id', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], ClinicalCase.prototype, "related_units", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ClinicalCase.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ClinicalCase.prototype, "updated_at", void 0);
exports.ClinicalCase = ClinicalCase = __decorate([
    (0, typeorm_1.Entity)('clinical_cases')
], ClinicalCase);
// src/dto/clinical-cases/create-clinical-case.dto.ts
const class_validator_1 = require("class-validator");
class CreateClinicalCaseDto {
    constructor() {
        this.is_published = false;
    }
}
exports.CreateClinicalCaseDto = CreateClinicalCaseDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateClinicalCaseDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateClinicalCaseDto.prototype, "patient_history", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateClinicalCaseDto.prototype, "clinical_findings", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateClinicalCaseDto.prototype, "lab_results", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateClinicalCaseDto.prototype, "imaging_results", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateClinicalCaseDto.prototype, "diagnosis", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateClinicalCaseDto.prototype, "treatment", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateClinicalCaseDto.prototype, "outcome_and_follow_up", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateClinicalCaseDto.prototype, "learning_points", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateClinicalCaseDto.prototype, "questions", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CreateClinicalCaseDto.prototype, "related_unit_ids", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateClinicalCaseDto.prototype, "is_published", void 0);
