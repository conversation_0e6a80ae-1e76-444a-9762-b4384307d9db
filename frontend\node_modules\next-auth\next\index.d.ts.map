{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/next/index.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EACV,yBAAyB,EACzB,cAAc,EACd,eAAe,EAChB,MAAM,MAAM,CAAA;AACb,OAAO,EAAE,KAAK,WAAW,EAAE,MAAM,aAAa,CAAA;AAC9C,OAAO,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,IAAI,CAAA;AAC9C,OAAO,KAAK,EACV,gBAAgB,EAIhB,SAAS,EACV,MAAM,eAAe,CAAA;AAEtB,UAAU,mBAAmB;IAC3B,MAAM,EAAE,SAAS,CAAC;QAAE,QAAQ,EAAE,MAAM,EAAE,CAAA;KAAE,CAAC,CAAA;CAC1C;AA6FD,iBAAS,QAAQ,CAAC,OAAO,EAAE,WAAW,GAAG,GAAG,CAAA;AAC5C,iBAAS,QAAQ,CACf,GAAG,EAAE,cAAc,EACnB,GAAG,EAAE,eAAe,EACpB,OAAO,EAAE,WAAW,GACnB,GAAG,CAAA;AAEN,iBAAS,QAAQ,CACf,GAAG,EAAE,WAAW,EAChB,GAAG,EAAE,mBAAmB,EACxB,OAAO,EAAE,WAAW,GACnB,GAAG,CAAA;AAsCN,eAAe,QAAQ,CAAA;AAEvB,aAAK,uBAAuB,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,GAAG;IACvE,SAAS,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,GAAG;QACtD,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,KAAK,GAAG,CAAA;KACpE,CAAA;CACF,CAAA;AAED,aAAK,sBAAsB,CAAC,CAAC,SAAS,uBAAuB,IACzD,CAAC,yBAAyB,CAAC,KAAK,CAAC,EAAE,yBAAyB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GACvE,CAAC,cAAc,EAAE,eAAe,EAAE,CAAC,CAAC,GACpC,CAAC,CAAC,CAAC,GACH,EAAE,CAAA;AAEN,wBAAsB,gBAAgB,CACpC,CAAC,SAAS,uBAAuB,EACjC,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,SAAS;IAAE,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,MAAM,CAAC,CAAA;CAAE,GAC/D,CAAC,GACD,OAAO,EACX,GAAG,IAAI,EAAE,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,CAgDvD;AAID,gDAAgD;AAChD,wBAAsB,yBAAyB,CAC7C,CAAC,SAAS,uBAAuB,EACjC,CAAC,GAAG,CAAC,CAAC,WAAW,CAAC,SAAS;IAAE,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,MAAM,CAAC,CAAA;CAAE,GAC/D,CAAC,GACD,OAAO,EACX,GAAG,IAAI,EAAE,sBAAsB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,CASvD;AAED,OAAO,CAAC,MAAM,CAAC;IAEb,UAAU,MAAM,CAAC;QACf,UAAU,UAAU;YAClB,YAAY,CAAC,EAAE,MAAM,CAAA;YACrB,eAAe,CAAC,EAAE,MAAM,CAAA;YACxB,WAAW,CAAC,EAAE,MAAM,CAAA;YACpB,MAAM,CAAC,EAAE,GAAG,CAAA;SACb;KACF;CACF"}