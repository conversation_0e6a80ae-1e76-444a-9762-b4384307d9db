!function(n,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("preact"),require("preact/hooks")):"function"==typeof define&&define.amd?define(["exports","preact","preact/hooks"],e):e((n||self).preactCompat={},n.preact,n.preactHooks)}(this,function(n,e,t){function r(n,e){for(var t in e)n[t]=e[t];return n}function u(n,e){for(var t in n)if("__source"!==t&&!(t in e))return!0;for(var r in e)if("__source"!==r&&n[r]!==e[r])return!0;return!1}function o(n,e){var r=e(),u=t.useState({t:{__:r,u:e}}),o=u[0].t,c=u[1];return t.useLayoutEffect(function(){o.__=r,o.u=e,i(o)&&c({t:o})},[n,r,e]),t.useEffect(function(){return i(o)&&c({t:o}),n(function(){i(o)&&c({t:o})})},[n]),r}function i(n){var e,t,r=n.u,u=n.__;try{var o=r();return!((e=u)===(t=o)&&(0!==e||1/e==1/t)||e!=e&&t!=t)}catch(n){return!0}}function c(n){n()}function f(n){return n}function l(){return[!1,c]}var a=t.useLayoutEffect;function s(n,e){this.props=n,this.context=e}function h(n,t){function r(n){var e=this.props.ref,r=e==n.ref;return!r&&e&&(e.call?e(null):e.current=null),t?!t(this.props,n)||!r:u(this.props,n)}function o(t){return this.shouldComponentUpdate=r,e.createElement(n,t)}return o.displayName="Memo("+(n.displayName||n.name)+")",o.prototype.isReactComponent=!0,o.__f=!0,o}(s.prototype=new e.Component).isPureReactComponent=!0,s.prototype.shouldComponentUpdate=function(n,e){return u(this.props,n)||u(this.state,e)};var d=e.options.__b;e.options.__b=function(n){n.type&&n.type.__f&&n.ref&&(n.props.ref=n.ref,n.ref=null),d&&d(n)};var v="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function m(n){function e(e){var t=r({},e);return delete t.ref,n(t,e.ref||null)}return e.$$typeof=v,e.render=e,e.prototype.isReactComponent=e.__f=!0,e.displayName="ForwardRef("+(n.displayName||n.name)+")",e}var p=function(n,t){return null==n?null:e.toChildArray(e.toChildArray(n).map(t))},b={map:p,forEach:p,count:function(n){return n?e.toChildArray(n).length:0},only:function(n){var t=e.toChildArray(n);if(1!==t.length)throw"Children.only";return t[0]},toArray:e.toChildArray},y=e.options.__e;e.options.__e=function(n,e,t,r){if(n.then)for(var u,o=e;o=o.__;)if((u=o.__c)&&u.__c)return null==e.__e&&(e.__e=t.__e,e.__k=t.__k),u.__c(n,e);y(n,e,t,r)};var _=e.options.unmount;function g(n,e,t){return n&&(n.__c&&n.__c.__H&&(n.__c.__H.__.forEach(function(n){"function"==typeof n.__c&&n.__c()}),n.__c.__H=null),null!=(n=r({},n)).__c&&(n.__c.__P===t&&(n.__c.__P=e),n.__c.__e=!0,n.__c=null),n.__k=n.__k&&n.__k.map(function(n){return g(n,e,t)})),n}function S(n,e,t){return n&&t&&(n.__v=null,n.__k=n.__k&&n.__k.map(function(n){return S(n,e,t)}),n.__c&&n.__c.__P===e&&(n.__e&&t.appendChild(n.__e),n.__c.__e=!0,n.__c.__P=t)),n}function E(){this.__u=0,this.o=null,this.__b=null}function C(n){var e=n.__.__c;return e&&e.__a&&e.__a(n)}function x(n){var t,r,u;function o(o){if(t||(t=n()).then(function(n){r=n.default||n},function(n){u=n}),u)throw u;if(!r)throw t;return e.createElement(r,o)}return o.displayName="Lazy",o.__f=!0,o}function O(){this.i=null,this.l=null}e.options.unmount=function(n){var e=n.__c;e&&e.__R&&e.__R(),e&&32&n.__u&&(n.type=null),_&&_(n)},(E.prototype=new e.Component).__c=function(n,e){var t=e.__c,r=this;null==r.o&&(r.o=[]),r.o.push(t);var u=C(r.__v),o=!1,i=function(){o||(o=!0,t.__R=null,u?u(c):c())};t.__R=i;var c=function(){if(!--r.__u){if(r.state.__a){var n=r.state.__a;r.__v.__k[0]=S(n,n.__c.__P,n.__c.__O)}var e;for(r.setState({__a:r.__b=null});e=r.o.pop();)e.forceUpdate()}};r.__u++||32&e.__u||r.setState({__a:r.__b=r.__v.__k[0]}),n.then(i,i)},E.prototype.componentWillUnmount=function(){this.o=[]},E.prototype.render=function(n,t){if(this.__b){if(this.__v.__k){var r=document.createElement("div"),u=this.__v.__k[0].__c;this.__v.__k[0]=g(this.__b,r,u.__O=u.__P)}this.__b=null}var o=t.__a&&e.createElement(e.Fragment,null,n.fallback);return o&&(o.__u&=-33),[e.createElement(e.Fragment,null,t.__a?null:n.children),o]};var R=function(n,e,t){if(++t[1]===t[0]&&n.l.delete(e),n.props.revealOrder&&("t"!==n.props.revealOrder[0]||!n.l.size))for(t=n.i;t;){for(;t.length>3;)t.pop()();if(t[1]<t[0])break;n.i=t=t[2]}};function w(n){return this.getChildContext=function(){return n.context},n.children}function j(n){var t=this,r=n.h;if(t.componentWillUnmount=function(){e.render(null,t.v),t.v=null,t.h=null},t.h&&t.h!==r&&t.componentWillUnmount(),!t.v){for(var u=t.__v;null!==u&&!u.__m&&null!==u.__;)u=u.__;t.h=r,t.v={nodeType:1,parentNode:r,childNodes:[],__k:{__m:u.__m},contains:function(){return!0},insertBefore:function(n,e){this.childNodes.push(n),t.h.insertBefore(n,e)},removeChild:function(n){this.childNodes.splice(this.childNodes.indexOf(n)>>>1,1),t.h.removeChild(n)}}}e.render(e.createElement(w,{context:t.context},n.__v),t.v)}function k(n,t){var r=e.createElement(j,{__v:n,h:t});return r.containerInfo=t,r}(O.prototype=new e.Component).__a=function(n){var e=this,t=C(e.__v),r=e.l.get(n);return r[0]++,function(u){var o=function(){e.props.revealOrder?(r.push(u),R(e,n,r)):u()};t?t(o):o()}},O.prototype.render=function(n){this.i=null,this.l=new Map;var t=e.toChildArray(n.children);n.revealOrder&&"b"===n.revealOrder[0]&&t.reverse();for(var r=t.length;r--;)this.l.set(t[r],this.i=[1,0,this.i]);return n.children},O.prototype.componentDidUpdate=O.prototype.componentDidMount=function(){var n=this;this.l.forEach(function(e,t){R(n,t,e)})};var T="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,I=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,N=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,M=/[A-Z0-9]/g,A="undefined"!=typeof document,D=function(n){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(n)};function L(n,t,r){return null==t.__k&&(t.textContent=""),e.render(n,t),"function"==typeof r&&r(),n?n.__c:null}function F(n,t,r){return e.hydrate(n,t),"function"==typeof r&&r(),n?n.__c:null}e.Component.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(n){Object.defineProperty(e.Component.prototype,n,{configurable:!0,get:function(){return this["UNSAFE_"+n]},set:function(e){Object.defineProperty(this,n,{configurable:!0,writable:!0,value:e})}})});var U=e.options.event;function V(){}function W(){return this.cancelBubble}function P(){return this.defaultPrevented}e.options.event=function(n){return U&&(n=U(n)),n.persist=V,n.isPropagationStopped=W,n.isDefaultPrevented=P,n.nativeEvent=n};var z,B={enumerable:!1,configurable:!0,get:function(){return this.class}},H=e.options.vnode;e.options.vnode=function(n){"string"==typeof n.type&&function(n){var t=n.props,r=n.type,u={},o=-1===r.indexOf("-");for(var i in t){var c=t[i];if(!("value"===i&&"defaultValue"in t&&null==c||A&&"children"===i&&"noscript"===r||"class"===i||"className"===i)){var f=i.toLowerCase();"defaultValue"===i&&"value"in t&&null==t.value?i="value":"download"===i&&!0===c?c="":"translate"===f&&"no"===c?c=!1:"o"===f[0]&&"n"===f[1]?"ondoubleclick"===f?i="ondblclick":"onchange"!==f||"input"!==r&&"textarea"!==r||D(t.type)?"onfocus"===f?i="onfocusin":"onblur"===f?i="onfocusout":N.test(i)&&(i=f):f=i="oninput":o&&I.test(i)?i=i.replace(M,"-$&").toLowerCase():null===c&&(c=void 0),"oninput"===f&&u[i=f]&&(i="oninputCapture"),u[i]=c}}"select"==r&&u.multiple&&Array.isArray(u.value)&&(u.value=e.toChildArray(t.children).forEach(function(n){n.props.selected=-1!=u.value.indexOf(n.props.value)})),"select"==r&&null!=u.defaultValue&&(u.value=e.toChildArray(t.children).forEach(function(n){n.props.selected=u.multiple?-1!=u.defaultValue.indexOf(n.props.value):u.defaultValue==n.props.value})),t.class&&!t.className?(u.class=t.class,Object.defineProperty(u,"className",B)):(t.className&&!t.class||t.class&&t.className)&&(u.class=u.className=t.className),n.props=u}(n),n.$$typeof=T,H&&H(n)};var q=e.options.__r;e.options.__r=function(n){q&&q(n),z=n.__c};var Z=e.options.diffed;e.options.diffed=function(n){Z&&Z(n);var e=n.props,t=n.__e;null!=t&&"textarea"===n.type&&"value"in e&&e.value!==t.value&&(t.value=null==e.value?"":e.value),z=null};var Y={ReactCurrentDispatcher:{current:{readContext:function(n){return z.__n[n.__c].props.value},useCallback:t.useCallback,useContext:t.useContext,useDebugValue:t.useDebugValue,useDeferredValue:f,useEffect:t.useEffect,useId:t.useId,useImperativeHandle:t.useImperativeHandle,useInsertionEffect:a,useLayoutEffect:t.useLayoutEffect,useMemo:t.useMemo,useReducer:t.useReducer,useRef:t.useRef,useState:t.useState,useSyncExternalStore:o,useTransition:l}}},$="18.3.1";function G(n){return e.createElement.bind(null,n)}function J(n){return!!n&&n.$$typeof===T}function K(n){return J(n)&&n.type===e.Fragment}function Q(n){return!!n&&!!n.displayName&&("string"==typeof n.displayName||n.displayName instanceof String)&&n.displayName.startsWith("Memo(")}function X(n){return J(n)?e.cloneElement.apply(null,arguments):n}function nn(n){return!!n.__k&&(e.render(null,n),!0)}function en(n){return n&&(n.base||1===n.nodeType&&n)||null}var tn=function(n,e){return n(e)},rn=function(n,e){return n(e)},un=e.Fragment,on=J,cn={useState:t.useState,useId:t.useId,useReducer:t.useReducer,useEffect:t.useEffect,useLayoutEffect:t.useLayoutEffect,useInsertionEffect:a,useTransition:l,useDeferredValue:f,useSyncExternalStore:o,startTransition:c,useRef:t.useRef,useImperativeHandle:t.useImperativeHandle,useMemo:t.useMemo,useCallback:t.useCallback,useContext:t.useContext,useDebugValue:t.useDebugValue,version:$,Children:b,render:L,hydrate:F,unmountComponentAtNode:nn,createPortal:k,createElement:e.createElement,createContext:e.createContext,createFactory:G,cloneElement:X,createRef:e.createRef,Fragment:e.Fragment,isValidElement:J,isElement:on,isFragment:K,isMemo:Q,findDOMNode:en,Component:e.Component,PureComponent:s,memo:h,forwardRef:m,flushSync:rn,unstable_batchedUpdates:tn,StrictMode:un,Suspense:E,SuspenseList:O,lazy:x,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:Y};Object.defineProperty(n,"Component",{enumerable:!0,get:function(){return e.Component}}),Object.defineProperty(n,"Fragment",{enumerable:!0,get:function(){return e.Fragment}}),Object.defineProperty(n,"createContext",{enumerable:!0,get:function(){return e.createContext}}),Object.defineProperty(n,"createElement",{enumerable:!0,get:function(){return e.createElement}}),Object.defineProperty(n,"createRef",{enumerable:!0,get:function(){return e.createRef}}),n.Children=b,n.PureComponent=s,n.StrictMode=un,n.Suspense=E,n.SuspenseList=O,n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Y,n.cloneElement=X,n.createFactory=G,n.createPortal=k,n.default=cn,n.findDOMNode=en,n.flushSync=rn,n.forwardRef=m,n.hydrate=F,n.isElement=on,n.isFragment=K,n.isMemo=Q,n.isValidElement=J,n.lazy=x,n.memo=h,n.render=L,n.startTransition=c,n.unmountComponentAtNode=nn,n.unstable_batchedUpdates=tn,n.useDeferredValue=f,n.useInsertionEffect=a,n.useSyncExternalStore=o,n.useTransition=l,n.version=$,Object.keys(t).forEach(function(e){"default"===e||n.hasOwnProperty(e)||Object.defineProperty(n,e,{enumerable:!0,get:function(){return t[e]}})})});
//# sourceMappingURL=compat.umd.js.map
