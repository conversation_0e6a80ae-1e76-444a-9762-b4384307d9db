"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenBlacklistService = void 0;
const common_1 = require("@nestjs/common");
const cache_service_1 = require("../../cache/cache.service");
const jwt_1 = require("@nestjs/jwt");
let TokenBlacklistService = class TokenBlacklistService {
    constructor(cacheService, jwtService) {
        this.cacheService = cacheService;
        this.jwtService = jwtService;
        this.blacklistPrefix = 'token:blacklist:';
        this.defaultTTL = 24 * 60 * 60; // 24 hours in seconds
    }
    async addToBlacklist(token, expires) {
        await this.cacheService.set(`${this.blacklistPrefix}${token}`, true, expires);
    }
    async blacklistToken(token) {
        try {
            // Decode token to get expiration
            const decoded = this.jwtService.decode(token);
            if (!decoded || typeof decoded === 'string') {
                throw new Error('Invalid token format');
            }
            // Calculate TTL based on token expiration
            const exp = decoded.exp;
            const now = Math.floor(Date.now() / 1000);
            const ttl = exp ? exp - now : this.defaultTTL;
            // Add to blacklist with expiration
            await this.cacheService.set(`${this.blacklistPrefix}${token}`, true, ttl);
        }
        catch (error) {
            // If token is invalid, blacklist it for default period
            await this.cacheService.set(`${this.blacklistPrefix}${token}`, true, this.defaultTTL);
        }
    }
    async isBlacklisted(token) {
        return await this.cacheService.get(`${this.blacklistPrefix}${token}`) === true;
    }
    async clearExpiredTokens() {
        // This is handled automatically by Redis TTL
        return;
    }
};
exports.TokenBlacklistService = TokenBlacklistService;
exports.TokenBlacklistService = TokenBlacklistService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [cache_service_1.CacheService,
        jwt_1.JwtService])
], TokenBlacklistService);
