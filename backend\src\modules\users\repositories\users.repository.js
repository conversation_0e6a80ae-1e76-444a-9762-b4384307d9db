"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("../../../entities");
let UsersRepository = class UsersRepository {
    constructor(repository) {
        this.repository = repository;
    }
    async findByUsername(username) {
        return this.repository.findOne({ where: { username } });
    }
    async findById(id) {
        return this.repository.findOne({ where: { id } });
    }
    async findAll() {
        return this.repository.find();
    }
    async findByEmail(email) {
        return this.repository.findOne({ where: { email } });
    }
    async create(user) {
        const newUser = this.repository.create(user);
        return this.repository.save(newUser);
    }
    async update(id, userData) {
        await this.repository.update(id, userData);
    }
    async delete(id) {
        await this.repository.delete(id);
    }
    async exists(id) {
        const count = await this.repository.count({ where: { id } });
        return count > 0;
    }
};
exports.UsersRepository = UsersRepository;
exports.UsersRepository = UsersRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UsersRepository);
